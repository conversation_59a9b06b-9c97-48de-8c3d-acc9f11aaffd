"use client";
import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import {
  Typo<PERSON>,
  Card,
  CardBody,
  Button,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
  Alert,
} from "@material-tailwind/react";
import {
  ArrowLeft,
  Server,
  Plus,
  Globe,
  Shield,
  AlertCircle,
  Zap,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import NameserverManager from "@/components/domains/NameserverManager";
import DnsRecordManager from "@/components/domains/DnsRecordManager";
import { toast } from "react-toastify";

export default function DomainDnsManagementPage() {
  const { id } = useParams();
  const router = useRouter();
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");

  const [domain, setDomain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("records");
  const [error, setError] = useState(null);

  // Function to update domain data
  const updateDomain = (updatedDomain) => {
    setDomain((prevDomain) => ({
      ...prevDomain,
      ...updatedDomain,
    }));
  };

  useEffect(() => {
    const fetchDomainData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get domain data from user domains
        const domainsResponse = await domainMngService.getUserDomains();
        
        if (domainsResponse.data && domainsResponse.data.domains) {
          const foundDomain = domainsResponse.data.domains.find(
            (d) => d.id === id
          );

          if (!foundDomain) {
            setError("Domain not found");
            return;
          }

          // Get detailed domain information
          try {
            const detailsResponse = await domainMngService.getDomainDetailsByName(
              foundDomain.name,
              "All"
            );

            if (detailsResponse.data.success && detailsResponse.data.domain) {
              const domainDetails = detailsResponse.data.domain;
              
              // Merge domain data with details
              const enrichedDomain = {
                ...foundDomain,
                ...domainDetails,
                // Ensure we keep the original ID
                id: foundDomain.id,
              };

              setDomain(enrichedDomain);
            } else {
              // Use basic domain data if details fetch fails
              setDomain(foundDomain);
            }
          } catch (detailsError) {
            console.warn("Could not fetch domain details:", detailsError);
            // Use basic domain data if details fetch fails
            setDomain(foundDomain);
          }
        } else {
          setError("Failed to load domain data");
        }
      } catch (error) {
        console.error("Error fetching domain data:", error);
        setError("Failed to load domain information");
        toast.error("Failed to load domain information");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchDomainData();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Server className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="text-gray-600">
            {t("loading")}...
          </Typography>
        </div>
      </div>
    );
  }

  if (error || !domain) {
    return (
      <div className="p-8 bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <Button
            variant="text"
            className="mb-6 text-blue-600 flex items-center gap-2"
            onClick={() => router.push("/client/domains")}
          >
            <ArrowLeft className="h-4 w-4" />
            {dt("back_to_domains")}
          </Button>

          <Alert color="red" className="max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            {error || "Domain not found"}
          </Alert>
        </div>
      </div>
    );
  }

  const tabsData = [
    {
      label: "DNS Records",
      value: "records",
      icon: Globe,
      description: "Manage A, AAAA, CNAME, MX, TXT, NS, and SRV records",
    },
    {
      label: "Nameservers",
      value: "nameservers",
      icon: Server,
      description: "Configure domain nameservers",
    },
  ];

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <Button
          variant="text"
          className="mb-6 text-blue-600 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>

        {/* Domain Info Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Globe className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <Typography variant="h1" className="text-3xl font-bold text-gray-800">
                {domain.name}
              </Typography>
              <div className="flex items-center gap-4 mt-2">
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium capitalize ${
                    domain.status === "active" || domain.currentstatus === "Active"
                      ? "bg-green-100 text-green-800"
                      : domain.status === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {domain.currentstatus || domain.status || "Unknown"}
                </span>
                {domain.orderid && (
                  <Typography className="text-sm text-gray-500">
                    Order ID: {domain.orderid}
                  </Typography>
                )}
              </div>
            </div>
          </div>
          
          <Typography className="text-gray-600">
            {t("manage_dns_settings_description", {
              defaultValue: "Configure DNS records and nameservers for your domain. Changes may take up to 24-48 hours to propagate globally.",
            })}
          </Typography>
        </div>

        {/* DNS Management Tabs */}
        <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
          <CardBody className="p-0">
            <Tabs value={activeTab} onChange={setActiveTab}>
              <TabsHeader className="bg-gray-50 p-1 m-6 mb-0">
                {tabsData.map(({ label, value, icon: Icon, description }) => (
                  <Tab key={value} value={value} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{label}</span>
                  </Tab>
                ))}
              </TabsHeader>

              <TabsBody className="p-6">
                <TabPanel value="records" className="p-0">
                  <div className="mb-4">
                    <Typography variant="h5" className="text-gray-800 mb-2">
                      DNS Records Management
                    </Typography>
                    <Typography className="text-gray-600 text-sm">
                      Manage A, AAAA, CNAME, MX, TXT, NS, and SRV records for your domain.
                    </Typography>
                  </div>
                  <DnsRecordManager domain={domain} onUpdate={updateDomain} />
                </TabPanel>

                <TabPanel value="nameservers" className="p-0">
                  <div className="mb-4">
                    <Typography variant="h5" className="text-gray-800 mb-2">
                      Nameserver Management
                    </Typography>
                    <Typography className="text-gray-600 text-sm">
                      Configure the nameservers that will handle DNS queries for your domain.
                    </Typography>
                  </div>
                  <NameserverManager domain={domain} onUpdate={updateDomain} />
                </TabPanel>
              </TabsBody>
            </Tabs>
          </CardBody>
        </Card>

        {/* Important Notes */}
        <Alert color="blue" className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <div>
            <Typography className="font-semibold mb-2">Important DNS Information:</Typography>
            <ul className="text-sm space-y-1">
              <li>• DNS changes may take 24-48 hours to propagate globally</li>
              <li>• Always test DNS changes before making them live</li>
              <li>• Keep backup records of your current DNS configuration</li>
              <li>• Some record types have specific requirements and restrictions</li>
            </ul>
          </div>
        </Alert>
      </div>
    </div>
  );
}
