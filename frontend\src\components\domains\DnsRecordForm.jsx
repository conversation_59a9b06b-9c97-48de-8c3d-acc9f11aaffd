"use client";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogBody,
  DialogFooter,
  Ty<PERSON>graphy,
  Button,
  Input,
  Textarea,
  Select,
  Option,
  Alert,
} from "@material-tailwind/react";
import { Save, X, AlertCircle, Info } from "lucide-react";
import {
  DNS_RECORD_TYPES,
  TTL_OPTIONS,
  DEFAULT_TTL,
  validateDnsRecord,
  checkRecordRestrictions,
  getDnsRecordTypes,
} from "@/constants/dnsRecords";

export default function DnsRecordForm({
  isOpen,
  onClose,
  onSubmit,
  initialData = null,
  domain,
  selectedType = "A",
  onTypeChange,
}) {
  const [formData, setFormData] = useState({
    type: selectedType,
    name: "",
    content: "",
    ttl: DEFAULT_TTL,
    priority: "",
    weight: "",
    port: "",
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!initialData;
  const recordTypes = getDnsRecordTypes();

  // Initialize form data
  useEffect(() => {
    if (initialData) {
      setFormData({
        type: initialData.type,
        name: initialData.name || "",
        content: initialData.content || "",
        ttl: initialData.ttl || DEFAULT_TTL,
        priority: initialData.priority || "",
        weight: initialData.weight || "",
        port: initialData.port || "",
      });
    } else {
      setFormData({
        type: selectedType,
        name: "",
        content: "",
        ttl: DEFAULT_TTL,
        priority: "",
        weight: "",
        port: "",
      });
    }
    setErrors({});
  }, [initialData, selectedType, isOpen]);

  // Get current record type configuration
  const currentRecordType = DNS_RECORD_TYPES[formData.type];

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Update parent component's selected type if type changes
    if (field === "type" && onTypeChange) {
      onTypeChange(value);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!currentRecordType) {
      newErrors.type = "Invalid record type";
      setErrors(newErrors);
      return false;
    }

    // Validate each required field
    currentRecordType.fields.forEach((field) => {
      const value = formData[field.name];

      // Check if required field is empty
      if (field.required && (!value || value.toString().trim() === "")) {
        newErrors[field.name] = `${field.label} is required`;
        return;
      }

      // Skip validation for empty optional fields
      if (!value || value.toString().trim() === "") {
        return;
      }

      // Validate field format
      const validation = validateDnsRecord(formData.type, field.name, value);
      if (!validation.isValid) {
        newErrors[field.name] = validation.message;
      }

      // Check number field ranges
      if (field.type === "number") {
        const numValue = parseInt(value);
        if (field.min !== undefined && numValue < field.min) {
          newErrors[field.name] = `Minimum value is ${field.min}`;
        }
        if (field.max !== undefined && numValue > field.max) {
          newErrors[field.name] = `Maximum value is ${field.max}`;
        }
      }
    });

    // Check record type restrictions
    const restrictionCheck = checkRecordRestrictions(formData.type, formData.name);
    if (!restrictionCheck.isValid) {
      newErrors.name = restrictionCheck.message;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Prepare submission data
      const submissionData = {
        type: formData.type,
        name: formData.name.trim(),
        content: formData.content.trim(),
        ttl: parseInt(formData.ttl),
      };

      // Add optional fields based on record type
      if (currentRecordType.fields.some((f) => f.name === "priority")) {
        submissionData.priority = formData.priority ? parseInt(formData.priority) : null;
      }
      if (currentRecordType.fields.some((f) => f.name === "weight")) {
        submissionData.weight = formData.weight ? parseInt(formData.weight) : null;
      }
      if (currentRecordType.fields.some((f) => f.name === "port")) {
        submissionData.port = formData.port ? parseInt(formData.port) : null;
      }

      await onSubmit(submissionData);
    } catch (error) {
      console.error("Error submitting DNS record:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render form field based on type
  const renderField = (field) => {
    const value = formData[field.name];
    const error = errors[field.name];

    switch (field.type) {
      case "select":
        if (field.name === "ttl") {
          return (
            <Select
              key={field.name}
              label={field.label}
              value={value.toString()}
              onChange={(val) => handleFieldChange(field.name, parseInt(val))}
              error={!!error}
            >
              {TTL_OPTIONS.map((option) => (
                <Option key={option.value} value={option.value.toString()}>
                  {option.label}
                </Option>
              ))}
            </Select>
          );
        }
        break;

      case "textarea":
        return (
          <Textarea
            key={field.name}
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            error={!!error}
            rows={3}
          />
        );

      case "number":
        return (
          <Input
            key={field.name}
            type="number"
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
            error={!!error}
          />
        );

      default:
        return (
          <Input
            key={field.name}
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            error={!!error}
          />
        );
    }
  };

  return (
    <Dialog open={isOpen} handler={onClose} size="lg">
      <form onSubmit={handleSubmit}>
        <DialogHeader className="flex items-center justify-between">
          <Typography variant="h4">
            {isEditing ? "Edit DNS Record" : "Add DNS Record"}
          </Typography>
          <Button variant="text" onClick={onClose} className="p-2">
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <DialogBody className="space-y-4 max-h-96 overflow-y-auto">
          {/* Record Type Selection */}
          <div>
            <Select
              label="Record Type"
              value={formData.type}
              onChange={(val) => handleFieldChange("type", val)}
              disabled={isEditing} // Don't allow changing type when editing
            >
              {recordTypes.map((type) => (
                <Option key={type.value} value={type.value}>
                  <div>
                    <Typography className="font-medium">{type.label}</Typography>
                    <Typography className="text-xs text-gray-600">
                      {type.description}
                    </Typography>
                  </div>
                </Option>
              ))}
            </Select>
            {errors.type && (
              <Typography className="text-red-500 text-xs mt-1">
                {errors.type}
              </Typography>
            )}
          </div>

          {/* Record Type Description */}
          {currentRecordType && (
            <Alert color="blue" className="py-2">
              <Info className="h-4 w-4" />
              <Typography className="text-sm">
                {currentRecordType.description}
              </Typography>
            </Alert>
          )}

          {/* Dynamic Form Fields */}
          {currentRecordType?.fields.map((field) => (
            <div key={field.name}>
              {renderField(field)}
              {errors[field.name] && (
                <Typography className="text-red-500 text-xs mt-1">
                  {errors[field.name]}
                </Typography>
              )}
            </div>
          ))}

          {/* Domain Context Helper */}
          <Alert color="amber" className="py-2">
            <AlertCircle className="h-4 w-4" />
            <div>
              <Typography className="text-sm font-medium">Domain Context:</Typography>
              <Typography className="text-xs">
                • Use "@" for the root domain ({domain?.name})
                <br />
                • Use "www" for www.{domain?.name}
                <br />
                • Use subdomain names like "mail" for mail.{domain?.name}
              </Typography>
            </div>
          </Alert>
        </DialogBody>

        <DialogFooter className="flex gap-2">
          <Button variant="outlined" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {isEditing ? "Updating..." : "Adding..."}
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                {isEditing ? "Update Record" : "Add Record"}
              </>
            )}
          </Button>
        </DialogFooter>
      </form>
    </Dialog>
  );
}
