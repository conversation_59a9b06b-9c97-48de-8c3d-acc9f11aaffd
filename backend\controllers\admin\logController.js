const AccountRole = require("../../constants/enums/account-role");
const AdminActivityLog = require("../../models/AdminActivityLog");
const User = require("../../models/User");
const mongoose = require("mongoose");
const logger = require("../../utils/globalLogger")("logController");

// Get admin activity logs with advanced filtering and pagination
exports.getAdminActivityLogs = async (req, res) => {
  try {
    // Extract page and limit from req.query.params (not directly from req.query)
    const page = parseInt(req.query.params?.page) || 1;
    const limit = parseInt(req.query.params?.limit) || 20;
    const skip = (page - 1) * limit;

    // Extract filter parameters
    const adminId = req.query.params?.adminId;
    const action = req.query.params?.action;
    const targetModel = req.query.params?.targetModel;
    const startDate = req.query.params?.startDate;
    const endDate = req.query.params?.endDate;
    const searchTerm = req.query.params?.search;

    logger.log("Received filter parameters:", {
      adminId,
      action,
      targetModel,
      startDate,
      endDate,
      searchTerm,
      allParams: req.query,
    });

    // Build query object
    let query = {
      // Always exclude VIEW actions
      action: { $ne: "VIEW" },
    };

    if (adminId) {
      // Make sure we're using a valid ObjectId
      try {
        if (mongoose.Types.ObjectId.isValid(adminId)) {
          query.admin = new mongoose.Types.ObjectId(adminId);
          logger.log("Using admin filter with ObjectId:", query.admin);
        } else {
          logger.log("Invalid admin ID format:", adminId);
        }
      } catch (err) {
        logger.error("Error converting admin ID to ObjectId:", err);
      }
    }

    if (action && action !== "VIEW") {
      // If a specific action is requested (except VIEW), use it
      query.action = action;
    }

    if (targetModel) {
      // Use case-insensitive matching for target model
      query.targetModel = { $regex: new RegExp("^" + targetModel + "$", "i") };
      console.log("Using target model filter:", query.targetModel);
    }

    // Date range filter
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }
      if (endDate) {
        // Set to end of day for the end date
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        query.timestamp.$lte = endOfDay;
      }
    }

    // Search functionality
    if (searchTerm) {
      // Search in action, targetModel, and admin details if populated
      query.$or = [
        { action: { $regex: searchTerm, $options: "i" } },
        { targetModel: { $regex: searchTerm, $options: "i" } },
      ];

      // If it's a valid ObjectId, also search by admin ID
      if (mongoose.Types.ObjectId.isValid(searchTerm)) {
        query.$or.push({ admin: new mongoose.Types.ObjectId(searchTerm) });
      }

      // When searching, we need to restructure the query to keep excluding VIEW actions
      const viewExclusion = { action: { $ne: "VIEW" } };
      query = { $and: [viewExclusion, { $or: query.$or }] };
    }

    // Debug the query before execution
    logger.log("Final query:", JSON.stringify(query, null, 2));

    // Get unique actions and target models for filters
    const uniqueActions = await AdminActivityLog.distinct("action");
    const uniqueTargetModels = await AdminActivityLog.distinct("targetModel");

    // Get admin list for filter dropdown
    const adminList = await User.find(
      { role: "Admin" },
      "firstName lastName email"
    ).lean();

    // Count total logs matching the query
    const totalLogs = await AdminActivityLog.countDocuments(query);

    // Fetch logs with pagination and sorting
    const logs = await AdminActivityLog.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .populate("admin", "firstName lastName email photo")
      .lean();

    logger.log(`Found ${logs.length} logs matching the query`);

    res.status(200).json({
      success: true,
      logs,
      filters: {
        actions: uniqueActions,
        targetModels: uniqueTargetModels,
        admins: adminList,
      },
      pagination: {
        totalItems: totalLogs,
        currentPage: page,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalLogs / limit),
      },
    });
  } catch (error) {
    logger.error("Error fetching admin activity logs:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching logs",
      error: error.message,
    });
  }
};

// Get available filters for activity logs
exports.getActivityLogFilters = async (_req, res) => {
  try {
    // Get all admins
    const admins = await User.find(
      { role: AccountRole.Admin },
      "firstName lastName email"
    );

    // Get distinct actions (excluding VIEW)
    const actions = await AdminActivityLog.distinct("action", {
      action: { $ne: "VIEW" },
    });

    // Get distinct target models
    const targetModels = await AdminActivityLog.distinct("targetModel");

    return res.status(200).json({
      success: true,
      admins,
      actions,
      targetModels,
    });
  } catch (error) {
    console.error("Error fetching activity log filters:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch activity log filters",
      error: error.message,
    });
  }
};

// Get activity log statistics
exports.getActivityLogStats = async (_req, res) => {
  try {
    // Get date range (default to last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    // Aggregate logs by action
    const byAction = await AdminActivityLog.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: "$action",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Aggregate logs by target model
    const byTargetModel = await AdminActivityLog.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: "$targetModel",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Aggregate logs by admin
    const byAdmin = await AdminActivityLog.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: "$admin",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
      {
        $limit: 10,
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "admin",
        },
      },
      {
        $unwind: {
          path: "$admin",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          count: 1,
          admin: {
            _id: 1,
            firstName: 1,
            lastName: 1,
            email: 1,
          },
        },
      },
    ]);

    // Format data for response
    const formattedByAction = byAction.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {});

    const formattedByTargetModel = byTargetModel.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {});

    // Return statistics
    return res.status(200).json({
      success: true,
      byAction: formattedByAction,
      byTargetModel: formattedByTargetModel,
      byAdmin: byAdmin,
    });
  } catch (error) {
    console.error("Error fetching activity log statistics:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching statistics",
      error: error.message,
    });
  }
};
