"use client";
import React from "react";
import { useTranslations } from "next-intl";
import { Shield, Check, X, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

export default function IdProtectPage() {
  const t = useTranslations();
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header/Banner Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <button
            onClick={() => router.back()}
            className="flex items-center text-blue-100 hover:text-white mb-6 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t("back", { defaultValue: "Retour" })}
          </button>

          <div className="flex items-center justify-center mb-8">
            <Shield className="h-16 w-16 text-blue-200 mr-4" />
            <div>
              <h1 className="text-4xl font-bold mb-2">ID Protect</h1>
              <p className="text-xl text-blue-100">
                {t("id_protect.subtitle", {
                  defaultValue:
                    "Protégez vos informations personnelles grâce au service de WHOIS Anonyme.",
                })}
              </p>
            </div>
          </div>

          <div className="text-center">
            <div className="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-6 py-3">
              <span className="text-2xl font-bold">
                {t("id_protect.price_only", { defaultValue: "à seulement" })}
              </span>
              <span className="text-3xl font-bold text-yellow-300 mx-2">
                39DH/an
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        {/* Description Section */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              {t("id_protect.protect_data_title", {
                defaultValue: "Protégez vos données personnelles",
              })}
            </h2>

            <div className="prose prose-lg text-gray-700 space-y-4">
              <p>
                {t("id_protect.description_1", {
                  defaultValue:
                    "Lorsque vous enregistrez un nom de domaine, son Whois liste vos informations personnelles (votre nom, nom de la société, adresse postale, numéro de téléphone, e-mail, etc.). Ces informations deviennent ainsi accessibles à tout le monde et risquent d'être exploitées par des personnes malintentionnées (spams, canulars téléphoniques, usurpation d'identité, etc.).",
                })}
              </p>

              <p>
                {t("id_protect.description_2", {
                  defaultValue:
                    "Grâce au service ID Protect, vous mettez fin immédiatement à ce genre de pratiques malveillantes. Lorsque vous activez ce service, nous remplaçons vos coordonnées dans les informations Whois par nos coordonnées génériques, masquant ainsi vos données personnelles.",
                })}
              </p>
            </div>
          </div>
        </div>

        {/* Comparison Section */}
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            {t("id_protect.comparison_title", {
              defaultValue: "Comparaison avec et sans protection",
            })}
          </h2>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Non Protected */}
            <div className="bg-white rounded-xl shadow-sm border-2 border-red-200">
              <div className="bg-red-50 px-6 py-4 rounded-t-xl border-b border-red-200">
                <div className="flex items-center">
                  <X className="h-6 w-6 text-red-600 mr-3" />
                  <h3 className="text-xl font-bold text-red-800">
                    {t("id_protect.not_protected", {
                      defaultValue: "NON PROTÉGÉ",
                    })}
                  </h3>
                </div>
                <p className="text-red-700 mt-2">
                  {t("id_protect.shows_personal_data", {
                    defaultValue: "Le Whois affiche vos données personnelles",
                  })}
                </p>
              </div>

              <div className="p-6">
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant name:
                    </span>
                    <span className="text-red-600">Votre nom & prénom</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Organization:
                    </span>
                    <span className="text-red-600">Votre société SARL</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Street:
                    </span>
                    <span className="text-red-600">Votre adresse postale</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant City:
                    </span>
                    <span className="text-red-600">Casablanca</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant State:
                    </span>
                    <span className="text-red-600">Grand Casablanca</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Postal Code:
                    </span>
                    <span className="text-red-600">20000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Country:
                    </span>
                    <span className="text-red-600">Maroc</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Phone:
                    </span>
                    <span className="text-red-600">+212.12345676</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Email:
                    </span>
                    <span className="text-red-600"><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>

            {/* Protected */}
            <div className="bg-white rounded-xl shadow-sm border-2 border-green-200">
              <div className="bg-green-50 px-6 py-4 rounded-t-xl border-b border-green-200">
                <div className="flex items-center">
                  <Check className="h-6 w-6 text-green-600 mr-3" />
                  <h3 className="text-xl font-bold text-green-800">
                    {t("id_protect.protected_by_idp", {
                      defaultValue: "PROTÉGÉ PAR IDP",
                    })}
                  </h3>
                </div>
                <p className="text-green-700 mt-2">
                  {t("id_protect.personal_data_protected", {
                    defaultValue: "Vos données personnelles sont protégées",
                  })}
                </p>
              </div>

              <div className="p-6">
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant name:
                    </span>
                    <span className="text-green-600">Domain Admin</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Organization:
                    </span>
                    <span className="text-green-600">Privacy Protect, LLC</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Street:
                    </span>
                    <span className="text-green-600">10 Corporate Drive</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant City:
                    </span>
                    <span className="text-green-600">Burlington</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant State:
                    </span>
                    <span className="text-green-600">MA</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Postal Code:
                    </span>
                    <span className="text-green-600">01803</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Country:
                    </span>
                    <span className="text-green-600">US</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Phone:
                    </span>
                    <span className="text-green-600">*************</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      Registrant Email:
                    </span>
                    <span className="text-green-600">
                      <EMAIL>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="max-w-4xl mx-auto mt-12">
          <div className="bg-blue-50 rounded-xl border border-blue-200 p-8">
            <div className="flex items-start">
              <Shield className="h-8 w-8 text-blue-600 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-xl font-bold text-blue-900 mb-4">
                  {t("id_protect.how_it_works", {
                    defaultValue: "Comment ça fonctionne ?",
                  })}
                </h3>
                <p className="text-blue-800 mb-4">
                  {t("id_protect.how_it_works_description", {
                    defaultValue:
                      "Même si vos coordonnées sont masquées, l'email mentionné sur le Whois joue le rôle d'un anti-spam/auto-répondeur et redirige les personnes qui souhaitent vous contacter vers le formulaire de contact sur le lien suivant: http://www.privacyprotect.org. De même, les appels téléphoniques sont transmis à un répondeur automatique qui invite les appelants à vous contacter par e-mail à l'adresse affichée dans le Whois de votre nom de domaine.",
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Restrictions Notice */}
        <div className="max-w-4xl mx-auto mt-8">
          <div className="bg-yellow-50 rounded-xl border border-yellow-200 p-6">
            <div className="flex items-start">
              <div className="bg-yellow-100 rounded-full p-2 mr-4">
                <span className="text-yellow-600 font-bold text-sm">!</span>
              </div>
              <div>
                <h4 className="font-bold text-yellow-800 mb-2">
                  {t("id_protect.restrictions_title", {
                    defaultValue: "Restrictions importantes",
                  })}
                </h4>
                <p className="text-yellow-700">
                  {t("id_protect.restrictions_description", {
                    defaultValue:
                      "En raison des restrictions imposées par les registres de certaines extensions, ces dernières ne prennent pas en charge l'option ID Protect.",
                  })}
                  <button className="text-yellow-800 underline hover:text-yellow-900 ml-1">
                    {t("id_protect.click_for_details", {
                      defaultValue: "Cliquez ici pour plus de détails.",
                    })}
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="max-w-4xl mx-auto mt-12 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl text-white p-8">
            <h3 className="text-2xl font-bold mb-4">
              {t("id_protect.cta_title", {
                defaultValue: "Protégez vos données dès maintenant",
              })}
            </h3>
            <p className="text-blue-100 mb-6">
              {t("id_protect.cta_description", {
                defaultValue:
                  "Activez ID Protect lors de l'enregistrement de votre domaine pour seulement 39DH/an",
              })}
            </p>
            <button
              onClick={() => router.push("/domains")}
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-bold hover:bg-blue-50 transition-colors"
            >
              {t("id_protect.search_domains", {
                defaultValue: "Rechercher un domaine",
              })}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
