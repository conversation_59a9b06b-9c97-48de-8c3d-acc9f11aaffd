# Global Logging System Guide

This backend uses a comprehensive global logging system that allows you to control ALL console output across the entire application.

## 🎯 Features

- **Disable ALL console logs** by default across the entire backend
- **Selectively enable logging** for specific functions/modules
- **Environment variable control** for easy configuration
- **No code removal** - all original console statements are preserved

## 🚀 Quick Start

### Option 1: Hide All Logs (Recommended for Production)
```bash
# In your .env file - don't set any logging variables
# OR explicitly set:
DISABLE_ALL_LOGS=true
```
**Result**: Only whitelisted functions (like `createOrder`) will show logs.

### Option 2: Show All Logs (Development/Debugging)
```bash
# In your .env file
ENABLE_ALL_LOGS=true
```
**Result**: ALL console statements from everywhere will be visible.

### Option 3: Default Behavior
```bash
# Don't set either variable, or set both to false
ENABLE_ALL_LOGS=false
DISABLE_ALL_LOGS=false
```
**Result**: Only whitelisted functions will show logs.

## 📝 How to Use in Your Code

### For New Code (Recommended)
```javascript
// At the top of your file
const logger = require("../utils/globalLogger")("yourFunctionName");

// In your function
exports.yourFunction = async (req, res) => {
  logger.log("This will only show if enabled for this function");
  logger.error("Error messages are also controlled");
  logger.warn("Warnings too");
};
```

### For Existing Code
The system automatically handles existing `console.log` statements:
- If `ENABLE_ALL_LOGS=true`: All existing console statements work normally
- If disabled: All existing console statements are hidden

## ⚙️ Configuration

### Adding Functions to Whitelist
Edit `backend/utils/globalLogger.js`:

```javascript
// Functions that should ALWAYS show logs
const ALWAYS_LOG_FUNCTIONS = [
  'createOrder',        // Already whitelisted
  'yourFunctionName',   // Add your function here
  'anotherFunction',    // Add more as needed
];

// Modules that should ALWAYS show logs  
const ALWAYS_LOG_MODULES = [
  'paymentController',  // Add module names here
  'authController',     // Add more as needed
];
```

## 🔧 Environment Variables

| Variable | Effect |
|----------|--------|
| `ENABLE_ALL_LOGS=true` | Shows ALL console statements everywhere |
| `DISABLE_ALL_LOGS=true` | Hides ALL console statements (overrides whitelist) |
| Not set or `false` | Only whitelisted functions/modules show logs |

## 📊 Examples

### Example 1: Only createOrder logs
```bash
# .env file (default)
# No logging variables set
```
**Output**:
```
[createOrder] Create Order......
[createOrder] Cart items: [...]
[createOrder] Processing domain item: example.com
```

### Example 2: All logs enabled
```bash
# .env file
ENABLE_ALL_LOGS=true
```
**Output**:
```
[createOrder] Create Order......
[getOrder] getting order: 12345
[logController] Received filter parameters: {...}
[adminLogger] Admin ID is required for activity logging
```

### Example 3: Completely silent
```bash
# .env file
DISABLE_ALL_LOGS=true
```
**Output**: (nothing - completely silent)

## 🎨 Benefits

1. **Clean Production Logs**: Hide all debug logs in production
2. **Selective Debugging**: Enable only the logs you need
3. **No Code Changes**: Works with existing console statements
4. **Easy Toggle**: Switch modes with environment variables
5. **Function Identification**: Each log shows which function it came from

## 🔍 Troubleshooting

- **No logs showing**: Check if `DISABLE_ALL_LOGS=true` is set
- **Too many logs**: Set `ENABLE_ALL_LOGS=false` or remove the variable
- **Missing specific logs**: Add the function name to the whitelist in `globalLogger.js`
