/**
 * Global Logging System
 *
 * This system allows you to:
 * 1. Disable ALL console logs across the entire backend by default
 * 2. Selectively enable logging for specific functions/modules
 * 3. Control logging via environment variables
 *
 * Usage:
 * const logger = require('./utils/globalLogger')('functionName');
 * logger.log('This will only show if enabled');
 * logger.error('Errors can be controlled too');
 */

// Store original console methods
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug,
};

// List of functions/modules that should ALWAYS show logs (whitelist)
const ALWAYS_LOG_FUNCTIONS = [
  "createOrder", // Add function names here that should always log
  "registerDomainsInOrder", // Enable logs for domain registration
  "getDomainDetailsByName", // Enable logs for domain details fetching
  "createOrUpdateDomainContact", // Enable logs for contact creation/updates
  "modifyContact", // Enable logs for contact modification
  "addContact", // Enable logs for contact creation
  // 'specificFunction',  // Uncomment and add more as needed
];

// List of modules that should ALWAYS show logs
const ALWAYS_LOG_MODULES = [
  // 'paymentController',  // Uncomment and add module names as needed
  // 'authController',
];

/**
 * Check if logging should be enabled for a given context
 * @param {string} context - Function name or module name
 * @returns {boolean}
 */
const shouldLog = (context) => {
  // If ENABLE_ALL_LOGS is true, show everything
  if (process.env.ENABLE_ALL_LOGS === "true") {
    return true;
  }

  // If DISABLE_ALL_LOGS is true, hide everything (overrides whitelist)
  if (process.env.DISABLE_ALL_LOGS === "true") {
    return false;
  }

  // Check if this function/module is in the whitelist
  return (
    ALWAYS_LOG_FUNCTIONS.includes(context) ||
    ALWAYS_LOG_MODULES.includes(context)
  );
};

/**
 * Create a logger for a specific context (function/module)
 * @param {string} context - The name of the function or module
 * @returns {object} Logger object with log, error, warn, info, debug methods
 */
const createLogger = (context) => {
  const isEnabled = shouldLog(context);

  return {
    log: (...args) => {
      if (isEnabled) {
        originalConsole.log(`[${context}]`, ...args);
      }
    },
    error: (...args) => {
      if (isEnabled) {
        originalConsole.error(`[${context}]`, ...args);
      }
    },
    warn: (...args) => {
      if (isEnabled) {
        originalConsole.warn(`[${context}]`, ...args);
      }
    },
    info: (...args) => {
      if (isEnabled) {
        originalConsole.info(`[${context}]`, ...args);
      }
    },
    debug: (...args) => {
      if (isEnabled) {
        originalConsole.debug(`[${context}]`, ...args);
      }
    },
  };
};

/**
 * Override global console methods to disable all logging by default
 * This affects ALL console.log statements across the entire backend
 */
const overrideGlobalConsole = () => {
  // Override console methods to disable ALL logging
  console.log = () => {}; // Disable all console.log
  console.error = () => {}; // Disable all console.error
  console.warn = () => {}; // Disable all console.warn
  console.info = () => {}; // Disable all console.info
  console.debug = () => {}; // Disable all console.debug
  console.trace = () => {}; // Disable all console.trace
  console.dir = () => {}; // Disable all console.dir
  console.table = () => {}; // Disable all console.table
  console.time = () => {}; // Disable all console.time
  console.timeEnd = () => {}; // Disable all console.timeEnd
  console.timeLog = () => {}; // Disable all console.timeLog
  console.group = () => {}; // Disable all console.group
  console.groupEnd = () => {}; // Disable all console.groupEnd
  console.groupCollapsed = () => {}; // Disable all console.groupCollapsed
  console.clear = () => {}; // Disable all console.clear
  console.count = () => {}; // Disable all console.count
  console.countReset = () => {}; // Disable all console.countReset
  console.assert = () => {}; // Disable all console.assert
};

/**
 * Restore original console methods
 */
const restoreGlobalConsole = () => {
  console.log = originalConsole.log;
  console.error = originalConsole.error;
  console.warn = originalConsole.warn;
  console.info = originalConsole.info;
  console.debug = originalConsole.debug;
};

/**
 * Initialize the global logging system
 * Call this once in your main server file
 */
const initializeGlobalLogging = () => {
  // Check environment variables
  const enableAllLogs = process.env.ENABLE_ALL_LOGS === "true";
  const disableAllLogs = process.env.DISABLE_ALL_LOGS === "true";

  if (enableAllLogs && !disableAllLogs) {
    // Keep original console methods - show all logs
    originalConsole.log(
      "🔊 Global logging enabled. All console statements will show."
    );
  } else {
    // Override and disable ALL console methods
    overrideGlobalConsole();

    // Log initialization with original console (before override)
    originalConsole.log(
      "🔇 Global logging disabled. All console statements are now hidden."
    );

    if (!disableAllLogs) {
      originalConsole.log(
        '💡 Use logger = require("./utils/globalLogger")("context") for selective logging.'
      );
    }
  }
};

// Immediately override console when this module is loaded
// This ensures ALL console statements are disabled from the start
const enableAllLogs = process.env.ENABLE_ALL_LOGS === "true";
const disableAllLogs = process.env.DISABLE_ALL_LOGS === "true";

if (!enableAllLogs || disableAllLogs) {
  // Override console immediately
  overrideGlobalConsole();
}

module.exports = createLogger;
module.exports.initializeGlobalLogging = initializeGlobalLogging;
module.exports.restoreGlobalConsole = restoreGlobalConsole;
module.exports.originalConsole = originalConsole;
