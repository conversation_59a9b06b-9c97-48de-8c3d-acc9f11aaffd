"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    var _domain_status, _domain_status1, _domain_status2, _domain_status3, _domain_status4, _domain_status5, _domain_status6, _domain_status7, _domain_status8, _domain_status9, _domain_raaVerification, _domain_gdpr, _domain_status10, _domain_status11, _domain_contacts, _domain_contacts1, _domain_contacts2, _domain_contacts3;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Utility function to format Unix timestamps\n    const formatDate = (unixTimestamp)=>{\n        if (!unixTimestamp) return \"Not available\";\n        try {\n            const date = new Date(parseInt(unixTimestamp) * 1000);\n            return date.toLocaleDateString(\"en-US\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } catch (error) {\n            return \"Invalid date\";\n        }\n    };\n    // Utility function to format domain status\n    const formatStatus = (status)=>{\n        if (!status) return \"unknown\";\n        return status.toLowerCase().replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    // Utility function to safely get contact information\n    const getContactInfo = function(contact, field) {\n        let fallback = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"Not available\";\n        if (!contact || typeof contact !== \"object\") {\n            return fallback;\n        }\n        return contact[field] || fallback;\n    };\n    // Utility function to check if contact exists and has data\n    const hasContactData = (contact)=>{\n        return contact && typeof contact === \"object\" && (contact.name || contact.email);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Try to get detailed information from the reseller API\n                try {\n                    var _detailsRes_data;\n                    console.log(\"\\uD83D\\uDD0D Fetching real domain details for:\", userDomain.name);\n                    // Get real domain details from reseller API\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details\n                    );\n                    console.log(\"✅ Real domain details from reseller API:\", detailsRes.data);\n                    const apiDomain = (_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain;\n                    if (apiDomain) {\n                        var _apiDomain_privacyProtection, _apiDomain_contactDetails, _apiDomain_contactDetails1, _apiDomain_contactDetails2, _apiDomain_contactDetails3;\n                        // Use real data from reseller API\n                        const combinedDomain = {\n                            id: userDomain.id,\n                            name: apiDomain.domainName || userDomain.name,\n                            status: apiDomain.status || userDomain.status,\n                            registrationDate: apiDomain.registrationDate || userDomain.registrationDate,\n                            expiryDate: apiDomain.expiryDate || userDomain.expiryDate,\n                            autoRenew: apiDomain.autoRenew || userDomain.autoRenew || false,\n                            registrar: \"ZTech Domains\",\n                            // Use real nameservers from API\n                            nameservers: apiDomain.nameservers && apiDomain.nameservers.length > 0 ? apiDomain.nameservers : userDomain.nameservers || [\n                                \"ns1.ztech\",\n                                \"ns2.ztech\",\n                                \"ns3.ztech\",\n                                \"ns4.ztech\"\n                            ],\n                            // Use real privacy protection data\n                            privacyProtection: ((_apiDomain_privacyProtection = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection === void 0 ? void 0 : _apiDomain_privacyProtection.enabled) || userDomain.privacyProtection || false,\n                            privacyProtectionDetails: apiDomain.privacyProtection,\n                            period: userDomain.period,\n                            price: userDomain.price,\n                            orderId: apiDomain.orderId || userDomain.orderId,\n                            orderStatus: apiDomain.orderStatus || userDomain.orderStatus,\n                            // Real contact details from API\n                            contacts: {\n                                registrant: ((_apiDomain_contactDetails = apiDomain.contactDetails) === null || _apiDomain_contactDetails === void 0 ? void 0 : _apiDomain_contactDetails.registrant) ? {\n                                    name: apiDomain.contactDetails.registrant.name,\n                                    email: apiDomain.contactDetails.registrant.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.registrant.telnocc, \" \").concat(apiDomain.contactDetails.registrant.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.registrant.address1, \", \").concat(apiDomain.contactDetails.registrant.city, \", \").concat(apiDomain.contactDetails.registrant.country, \" \").concat(apiDomain.contactDetails.registrant.zip),\n                                    company: apiDomain.contactDetails.registrant.company,\n                                    contactId: apiDomain.contactDetails.registrant.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                admin: ((_apiDomain_contactDetails1 = apiDomain.contactDetails) === null || _apiDomain_contactDetails1 === void 0 ? void 0 : _apiDomain_contactDetails1.admin) ? {\n                                    name: apiDomain.contactDetails.admin.name,\n                                    email: apiDomain.contactDetails.admin.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.admin.telnocc, \" \").concat(apiDomain.contactDetails.admin.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.admin.address1, \", \").concat(apiDomain.contactDetails.admin.city, \", \").concat(apiDomain.contactDetails.admin.country, \" \").concat(apiDomain.contactDetails.admin.zip),\n                                    company: apiDomain.contactDetails.admin.company,\n                                    contactId: apiDomain.contactDetails.admin.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                technical: ((_apiDomain_contactDetails2 = apiDomain.contactDetails) === null || _apiDomain_contactDetails2 === void 0 ? void 0 : _apiDomain_contactDetails2.tech) ? {\n                                    name: apiDomain.contactDetails.tech.name,\n                                    email: apiDomain.contactDetails.tech.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.tech.telnocc, \" \").concat(apiDomain.contactDetails.tech.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.tech.address1, \", \").concat(apiDomain.contactDetails.tech.city, \", \").concat(apiDomain.contactDetails.tech.country, \" \").concat(apiDomain.contactDetails.tech.zip),\n                                    company: apiDomain.contactDetails.tech.company,\n                                    contactId: apiDomain.contactDetails.tech.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                billing: ((_apiDomain_contactDetails3 = apiDomain.contactDetails) === null || _apiDomain_contactDetails3 === void 0 ? void 0 : _apiDomain_contactDetails3.billing) ? {\n                                    name: apiDomain.contactDetails.billing.name,\n                                    email: apiDomain.contactDetails.billing.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.billing.telnocc, \" \").concat(apiDomain.contactDetails.billing.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.billing.address1, \", \").concat(apiDomain.contactDetails.billing.city, \", \").concat(apiDomain.contactDetails.billing.country, \" \").concat(apiDomain.contactDetails.billing.zip),\n                                    company: apiDomain.contactDetails.billing.company,\n                                    contactId: apiDomain.contactDetails.billing.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                }\n                            },\n                            // Contact IDs for API operations\n                            contactIds: apiDomain.contacts,\n                            // Additional real data from API\n                            productCategory: apiDomain.productCategory,\n                            productKey: apiDomain.productKey,\n                            customerId: apiDomain.customerId,\n                            gdpr: apiDomain.gdpr,\n                            locks: apiDomain.locks,\n                            raaVerification: apiDomain.raaVerification,\n                            dnssec: apiDomain.dnssec,\n                            // Raw API response for debugging\n                            apiDetails: apiDomain,\n                            // Default DNS records (placeholder - would need separate API call)\n                            dnsRecords: [\n                                {\n                                    id: \"rec1\",\n                                    type: \"A\",\n                                    name: \"@\",\n                                    content: \"DNS records available via separate API\",\n                                    ttl: 3600\n                                }\n                            ]\n                        };\n                        setDomain(combinedDomain);\n                    } else {\n                        throw new Error(\"No domain data received from API\");\n                    }\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"ns1.ztech\",\n                            \"ns2.ztech\",\n                            \"ns3.ztech\",\n                            \"ns4.ztech\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            technical: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.togglePrivacyProtection(id, value);\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling privacy protection\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 329,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(((_domain_status = domain.status) === null || _domain_status === void 0 ? void 0 : _domain_status.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status1 = domain.status) === null || _domain_status1 === void 0 ? void 0 : _domain_status1.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status2 = domain.status) === null || _domain_status2 === void 0 ? void 0 : _domain_status2.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status3 = domain.status) === null || _domain_status3 === void 0 ? void 0 : _domain_status3.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(((_domain_status4 = domain.status) === null || _domain_status4 === void 0 ? void 0 : _domain_status4.toLowerCase()) || \"unknown\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        t(\"visit_website\", {\n                                            defaultValue: \"Visit Website\"\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 457,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Order ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 462,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                domain.orderId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 465,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(((_domain_status5 = domain.status) === null || _domain_status5 === void 0 ? void 0 : _domain_status5.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status6 = domain.status) === null || _domain_status6 === void 0 ? void 0 : _domain_status6.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status7 = domain.status) === null || _domain_status7 === void 0 ? void 0 : _domain_status7.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status8 = domain.status) === null || _domain_status8 === void 0 ? void 0 : _domain_status8.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(((_domain_status9 = domain.status) === null || _domain_status9 === void 0 ? void 0 : _domain_status9.toLowerCase()) || \"unknown\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.registrationDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.expiryDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: \"Security & Protection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                    checked: domain.privacyProtection,\n                                                                                    onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                                    color: \"blue\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded \".concat(domain.privacyProtection ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                                    children: domain.privacyProtection ? \"Enabled\" : \"Disabled\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 540,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderStatus && Array.isArray(domain.orderStatus) && domain.orderStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Domain Locks\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.orderStatus.map((lock, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded\",\n                                                                                    children: lock\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 556,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.domainStatus && Array.isArray(domain.domainStatus) && domain.domainStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registry Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 566,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.domainStatus.map((status, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                                                    children: status\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_raaVerification = domain.raaVerification) === null || _domain_raaVerification === void 0 ? void 0 : _domain_raaVerification.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"RAA Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.raaVerification.status === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerification.status === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerification.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_gdpr = domain.gdpr) === null || _domain_gdpr === void 0 ? void 0 : _domain_gdpr.enabled) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"GDPR Protection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.gdpr.enabled === \"true\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                            children: domain.gdpr.enabled === \"true\" ? \"Enabled\" : \"Disabled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                (((_domain_status10 = domain.status) === null || _domain_status10 === void 0 ? void 0 : _domain_status10.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" || domain.registrationError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registration Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-100 text-red-800 rounded\",\n                                                                                    children: \"Registration Failed\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 618,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                domain.registrationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-50 text-red-700 rounded\",\n                                                                                    children: domain.registrationError\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 622,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 617,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers && Array.isArray(domain.nameservers) && domain.nameservers.length > 0 ? domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 27\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_domain_status11 = domain.status) === null || _domain_status11 === void 0 ? void 0 : _domain_status11.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" ? \"Nameservers not available - Registration failed\" : \"No nameservers configured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: (updatedDomain)=>setDomain(updatedDomain)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: dt(\"manage_dns_records\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                                children: t(\"add_record\", {\n                                                                    defaultValue: \"Add Record\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"type\", {\n                                                                                    defaultValue: \"Type\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"name\", {\n                                                                                    defaultValue: \"Name\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 709,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"content\", {\n                                                                                    defaultValue: \"Content\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 712,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"ttl\", {\n                                                                                    defaultValue: \"TTL\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 715,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-500\",\n                                                                                children: t(\"actions\", {\n                                                                                    defaultValue: \"Actions\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 718,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                                    children: record.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 726,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 729,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.content\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 732,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.ttl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-right\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        variant: \"text\",\n                                                                                        className: \"text-blue-600\",\n                                                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/\").concat(record.id)),\n                                                                                        children: t(\"edit\", {\n                                                                                            defaultValue: \"Edit\"\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 739,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 738,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, record.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 725,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts = domain.contacts) === null || _domain_contacts === void 0 ? void 0 : _domain_contacts.registrant) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 778,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 782,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 789,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 802,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts1 = domain.contacts) === null || _domain_contacts1 === void 0 ? void 0 : _domain_contacts1.admin) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 815,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 819,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.admin, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 823,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.admin, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 826,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.admin, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 829,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.admin, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 833,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts2 = domain.contacts) === null || _domain_contacts2 === void 0 ? void 0 : _domain_contacts2.technical) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.technical, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 860,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.technical, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 863,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.technical, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.technical, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 870,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 885,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts3 = domain.contacts) === null || _domain_contacts3 === void 0 ? void 0 : _domain_contacts3.billing) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 891,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 895,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.billing, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 899,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.billing, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 902,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.billing, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 905,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.billing, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 909,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 915,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 938,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 346,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 345,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});