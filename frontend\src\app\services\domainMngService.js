import apiService from "../lib/apiService";

const domainMngService = {
  // Customer management
  customerSignup: (data) =>
    apiService.post("/domainMng/customer-signup", data, {
      withCredentials: true,
    }),

  // Domain operations
  checkDomainAvailability: (params) =>
    apiService.get("/domainMng/check-domain", {
      params,
      withCredentials: true,
    }),

  checkIdnDomainAvailability: (params) =>
    apiService.get("/domainMng/check-idn-domain", {
      params,
      withCredentials: true,
    }),

  checkPremiumDomainAvailability: (params) =>
    apiService.get("/domainMng/check-premium-domain", {
      params,
      withCredentials: true,
    }),

  suggestDomainNames: (params) =>
    apiService.get("/domainMng/suggest-names", {
      params,
      withCredentials: true,
    }),

  // Pricing information
  getDNPricing: (params) =>
    apiService.get("/domainMng/get-dn-pricing", {
      params,
      withCredentials: true,
    }),

  // Get reseller pricing
  getResellerPricing: (params) =>
    apiService.get("/domainMng/get-reseller-pricing", {
      params,
      withCredentials: true,
    }),

  // Add domain to cart
  addDomainToCart: (data) =>
    apiService.post("/domainMng/add-domain-to-cart", data, {
      withCredentials: true,
    }),

  // Add the new comprehensive search method
  // Expected params: { params: "domain-name-string" }
  searchDomains: (params) =>
    apiService.get("/domainMng/search-domains", {
      params,
      withCredentials: true,
    }),

  // Domain Management - User Domains
  getUserDomains: () =>
    apiService.get("/order/get-domain-orders", {
      withCredentials: true,
    }),

  // Get domain details
  getDomainDetails: (domainName) =>
    apiService.get("/domainMng/domain-details", {
      params: { domainName },
      withCredentials: true,
    }),

  // Get domain details by name (from registration system)
  getDomainDetailsByName: (domainName, options = "OrderDetails") =>
    apiService.get("/domainMng/domain-details-by-name", {
      params: { domainName, options },
      withCredentials: true,
    }),

  // Renew domain
  renewDomain: (data) =>
    apiService.post("/domainMng/renew-domain", data, {
      withCredentials: true,
    }),

  // Get domain order ID
  getDomainOrderId: (domainName) => {
    console.log(
      "🔧 Frontend service - getDomainOrderId called with:",
      domainName
    );
    console.log("🔧 Frontend service - params object:", { domainName });
    return apiService.get("/domainMng/domain-order-id", {
      params: { domainName },
      withCredentials: true,
    });
  },

  // Get customer default nameservers
  getCustomerDefaultNameservers: (customerId) =>
    apiService.get("/domainMng/customer-default-nameservers", {
      params: customerId ? { customerId } : {},
      withCredentials: true,
    }),

  // Modify nameservers
  modifyNameservers: (data) =>
    apiService.post("/domainMng/modify-nameservers", data, {
      withCredentials: true,
    }),

  // Enable privacy protection
  enablePrivacyProtection: (data) =>
    apiService.post("/domainMng/enable-privacy", data, {
      withCredentials: true,
    }),

  // Disable privacy protection
  disablePrivacyProtection: (data) =>
    apiService.post("/domainMng/disable-privacy", data, {
      withCredentials: true,
    }),

  // Purchase privacy protection
  purchasePrivacyProtection: (data) =>
    apiService.post("/domainMng/purchase-privacy", data, {
      withCredentials: true,
    }),

  // Modify privacy protection status
  modifyPrivacyProtection: (data) =>
    apiService.post("/domainMng/modify-privacy", data, {
      withCredentials: true,
    }),

  getDomainById: (domainId) =>
    apiService.get(`/domainMng/domains/${domainId}`, {
      withCredentials: true,
    }),

  // Domain Management - Nameservers
  updateNameservers: (domainId, nameservers) =>
    apiService.put(
      `/domainMng/domains/${domainId}/nameservers`,
      { nameservers },
      { withCredentials: true }
    ),

  // Domain Management - Auto Renewal
  toggleAutoRenewal: (domainId, autoRenew) =>
    apiService.put(
      `/domainMng/domains/${domainId}/auto-renew`,
      { autoRenew },
      { withCredentials: true }
    ),

  // Domain Management - Privacy Protection
  togglePrivacyProtection: (domainId, privacyEnabled) =>
    apiService.put(
      `/domainMng/domains/${domainId}/privacy`,
      { privacyEnabled },
      { withCredentials: true }
    ),

  // Domain Management - DNS Records
  activateDnsService: (orderId) =>
    apiService.post(
      "/domainMng/dns/activate",
      { orderId },
      { withCredentials: true }
    ),

  getDnsRecords: (domainId) =>
    apiService.get(`/domainMng/domains/${domainId}/dns`, {
      withCredentials: true,
    }),

  addDnsRecord: (domainId, record) =>
    apiService.post(
      `/domainMng/domains/${domainId}/dns`,
      { record },
      { withCredentials: true }
    ),

  updateDnsRecord: (domainId, recordId, record) =>
    apiService.put(
      `/domainMng/domains/${domainId}/dns/${recordId}`,
      { record },
      { withCredentials: true }
    ),

  deleteDnsRecord: (domainId, recordId) =>
    apiService.delete(`/domainMng/domains/${domainId}/dns/${recordId}`, {
      withCredentials: true,
    }),
};

export default domainMngService;
