"use client";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
  <PERSON><PERSON>,
  <PERSON><PERSON>Header,
  DialogBody,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>,
} from "@material-tailwind/react";
import {
  Edit,
  Trash2,
  Globe,
  Mail,
  FileText,
  Server,
  Link,
  Settings,
  AlertTriangle,
  Copy,
  ExternalLink,
} from "lucide-react";
import { TTL_OPTIONS } from "@/constants/dnsRecords";
import { toast } from "react-toastify";

export default function DnsRecordList({ records, onEdit, onDelete, domain }) {
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  // Get record type icon
  const getRecordTypeIcon = (type) => {
    const icons = {
      A: Globe,
      AAAA: Globe,
      CNAME: Link,
      MX: Mail,
      TXT: FileText,
      NS: Server,
      SRV: Settings,
    };
    const IconComponent = icons[type] || Globe;
    return <IconComponent className="h-4 w-4" />;
  };

  // Get record type color
  const getRecordTypeColor = (type) => {
    const colors = {
      A: "blue",
      AAAA: "blue",
      CNAME: "purple",
      MX: "green",
      TXT: "orange",
      NS: "gray",
      SRV: "pink",
    };
    return colors[type] || "gray";
  };

  // Format TTL display
  const formatTTL = (ttl) => {
    const option = TTL_OPTIONS.find((opt) => opt.value === ttl);
    return option ? option.label : `${ttl}s`;
  };

  // Format record name for display
  const formatRecordName = (name) => {
    if (name === "@") {
      return `@ (${domain?.name})`;
    }
    if (name === "") {
      return `@ (${domain?.name})`;
    }
    return `${name}.${domain?.name}`;
  };

  // Copy content to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success("Copied to clipboard");
    });
  };

  // Handle delete confirmation
  const handleDeleteClick = (record) => {
    setDeleteConfirm(record);
  };

  const handleDeleteConfirm = () => {
    if (deleteConfirm) {
      onDelete(deleteConfirm.id);
      setDeleteConfirm(null);
    }
  };

  // Render record content based on type
  const renderRecordContent = (record) => {
    const { type, content, priority, weight, port } = record;

    switch (type) {
      case "MX":
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="font-mono text-sm">{content}</span>
              <Button
                variant="text"
                size="sm"
                onClick={() => copyToClipboard(content)}
                className="p-1"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
            <Typography className="text-xs text-gray-500">
              Priority: {priority}
            </Typography>
          </div>
        );

      case "SRV":
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="font-mono text-sm">{content}</span>
              <Button
                variant="text"
                size="sm"
                onClick={() => copyToClipboard(content)}
                className="p-1"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
            <Typography className="text-xs text-gray-500">
              Priority: {priority}, Weight: {weight}, Port: {port}
            </Typography>
          </div>
        );

      case "TXT":
        const truncatedContent = content.length > 50 ? 
          `${content.substring(0, 50)}...` : content;
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="font-mono text-sm break-all">
                {truncatedContent}
              </span>
              <Button
                variant="text"
                size="sm"
                onClick={() => copyToClipboard(content)}
                className="p-1"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
            {content.length > 50 && (
              <Typography className="text-xs text-gray-500">
                Full content available in edit mode
              </Typography>
            )}
          </div>
        );

      default:
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono text-sm">{content}</span>
            <Button
              variant="text"
              size="sm"
              onClick={() => copyToClipboard(content)}
              className="p-1"
            >
              <Copy className="h-3 w-3" />
            </Button>
          </div>
        );
    }
  };

  if (records.length === 0) {
    return (
      <div className="text-center py-8">
        <Typography className="text-gray-500">
          No DNS records found for this filter.
        </Typography>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-3">
        {records.map((record) => (
          <Card key={record.id} className="border border-gray-200">
            <CardBody className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  {/* Record Header */}
                  <div className="flex items-center gap-3">
                    <Chip
                      value={record.type}
                      color={getRecordTypeColor(record.type)}
                      icon={getRecordTypeIcon(record.type)}
                      className="text-xs"
                    />
                    <Typography className="font-medium text-gray-900">
                      {formatRecordName(record.name)}
                    </Typography>
                    <Typography className="text-sm text-gray-500">
                      TTL: {formatTTL(record.ttl)}
                    </Typography>
                  </div>

                  {/* Record Content */}
                  <div className="pl-0">
                    {renderRecordContent(record)}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant="text"
                    size="sm"
                    onClick={() => onEdit(record)}
                    className="text-blue-600 hover:bg-blue-50 p-2"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="text"
                    size="sm"
                    onClick={() => handleDeleteClick(record)}
                    className="text-red-600 hover:bg-red-50 p-2"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!deleteConfirm}
        handler={() => setDeleteConfirm(null)}
        size="sm"
      >
        <DialogHeader className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-red-600" />
          Confirm Delete
        </DialogHeader>
        <DialogBody>
          <Alert color="red" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <Typography className="font-semibold">
              This action cannot be undone!
            </Typography>
          </Alert>
          
          {deleteConfirm && (
            <div className="space-y-2">
              <Typography>
                Are you sure you want to delete this DNS record?
              </Typography>
              
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Chip
                    value={deleteConfirm.type}
                    color={getRecordTypeColor(deleteConfirm.type)}
                    icon={getRecordTypeIcon(deleteConfirm.type)}
                    size="sm"
                  />
                  <Typography className="font-medium">
                    {formatRecordName(deleteConfirm.name)}
                  </Typography>
                </div>
                <Typography className="text-sm text-gray-600 font-mono">
                  {deleteConfirm.content}
                </Typography>
              </div>
              
              <Typography className="text-sm text-gray-600">
                Deleting this record may affect your domain's functionality. 
                DNS changes may take up to 24-48 hours to propagate.
              </Typography>
            </div>
          )}
        </DialogBody>
        <DialogFooter className="flex gap-2">
          <Button
            variant="outlined"
            onClick={() => setDeleteConfirm(null)}
          >
            Cancel
          </Button>
          <Button
            color="red"
            onClick={handleDeleteConfirm}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Delete Record
          </Button>
        </DialogFooter>
      </Dialog>
    </>
  );
}
