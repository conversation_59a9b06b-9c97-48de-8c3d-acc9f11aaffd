"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    var _domain_status, _domain_status1, _domain_status2, _domain_status3, _domain_status4, _domain_status5, _domain_status6, _domain_status7, _domain_status8, _domain_status9, _domain_privacyProtectionDetails, _domain_raaVerification, _domain_gdpr, _domain_status10, _domain_status11, _domain_contacts, _domain_contacts1, _domain_contacts2, _domain_contacts3;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Utility function to format Unix timestamps\n    const formatDate = (unixTimestamp)=>{\n        if (!unixTimestamp) return \"Not available\";\n        try {\n            const date = new Date(parseInt(unixTimestamp) * 1000);\n            return date.toLocaleDateString(\"en-US\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } catch (error) {\n            return \"Invalid date\";\n        }\n    };\n    // Utility function to format domain status\n    const formatStatus = (status)=>{\n        if (!status) return \"unknown\";\n        return status.toLowerCase().replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    // Utility function to safely get contact information\n    const getContactInfo = function(contact, field) {\n        let fallback = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"Not available\";\n        if (!contact || typeof contact !== \"object\") {\n            return fallback;\n        }\n        return contact[field] || fallback;\n    };\n    // Utility function to check if contact exists and has data\n    const hasContactData = (contact)=>{\n        return contact && typeof contact === \"object\" && (contact.name || contact.email);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Try to get detailed information from the reseller API\n                try {\n                    var _detailsRes_data, _apiDomain_privacyProtection, _apiDomain_privacyProtection1;\n                    console.log(\"\\uD83D\\uDD0D Fetching real domain details for:\", userDomain.name);\n                    // Get real domain details from reseller API\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details\n                    );\n                    console.log(\"✅ Real domain details from reseller API:\", detailsRes.data);\n                    const apiDomain = (_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain;\n                    // Debug privacy protection data\n                    console.log(\"\\uD83D\\uDD12 [PRIVACY DEBUG] API Privacy Data:\", {\n                        apiPrivacyProtection: apiDomain === null || apiDomain === void 0 ? void 0 : apiDomain.privacyProtection,\n                        userPrivacyProtection: userDomain.privacyProtection,\n                        apiPrivacyEnabled: apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection === void 0 ? void 0 : _apiDomain_privacyProtection.enabled,\n                        willUseApiData: (apiDomain === null || apiDomain === void 0 ? void 0 : (_apiDomain_privacyProtection1 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection1 === void 0 ? void 0 : _apiDomain_privacyProtection1.enabled) !== undefined\n                    });\n                    if (apiDomain) {\n                        var _apiDomain_privacyProtection2, _apiDomain_contactDetails, _apiDomain_contactDetails1, _apiDomain_contactDetails2, _apiDomain_contactDetails3;\n                        // Use real data from reseller API\n                        const combinedDomain = {\n                            id: userDomain.id,\n                            name: apiDomain.domainName || userDomain.name,\n                            status: apiDomain.status || userDomain.status,\n                            registrationDate: apiDomain.registrationDate || userDomain.registrationDate,\n                            expiryDate: apiDomain.expiryDate || userDomain.expiryDate,\n                            autoRenew: apiDomain.autoRenew || userDomain.autoRenew || false,\n                            registrar: \"ZTech Domains\",\n                            // Use real nameservers from API\n                            nameservers: apiDomain.nameservers && apiDomain.nameservers.length > 0 ? apiDomain.nameservers : userDomain.nameservers || [\n                                \"ns1.ztech\",\n                                \"ns2.ztech\",\n                                \"ns3.ztech\",\n                                \"ns4.ztech\"\n                            ],\n                            // Use real privacy protection data from reseller API (prioritize API data)\n                            privacyProtection: ((_apiDomain_privacyProtection2 = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection2 === void 0 ? void 0 : _apiDomain_privacyProtection2.enabled) !== undefined ? apiDomain.privacyProtection.enabled : userDomain.privacyProtection || false,\n                            privacyProtectionDetails: apiDomain.privacyProtection,\n                            period: userDomain.period,\n                            price: userDomain.price,\n                            orderId: apiDomain.orderId || userDomain.orderId,\n                            orderStatus: apiDomain.orderStatus || userDomain.orderStatus,\n                            // Real contact details from API\n                            contacts: {\n                                registrant: ((_apiDomain_contactDetails = apiDomain.contactDetails) === null || _apiDomain_contactDetails === void 0 ? void 0 : _apiDomain_contactDetails.registrant) ? {\n                                    name: apiDomain.contactDetails.registrant.name,\n                                    email: apiDomain.contactDetails.registrant.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.registrant.telnocc, \" \").concat(apiDomain.contactDetails.registrant.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.registrant.address1, \", \").concat(apiDomain.contactDetails.registrant.city, \", \").concat(apiDomain.contactDetails.registrant.country, \" \").concat(apiDomain.contactDetails.registrant.zip),\n                                    company: apiDomain.contactDetails.registrant.company,\n                                    contactId: apiDomain.contactDetails.registrant.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                admin: ((_apiDomain_contactDetails1 = apiDomain.contactDetails) === null || _apiDomain_contactDetails1 === void 0 ? void 0 : _apiDomain_contactDetails1.admin) ? {\n                                    name: apiDomain.contactDetails.admin.name,\n                                    email: apiDomain.contactDetails.admin.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.admin.telnocc, \" \").concat(apiDomain.contactDetails.admin.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.admin.address1, \", \").concat(apiDomain.contactDetails.admin.city, \", \").concat(apiDomain.contactDetails.admin.country, \" \").concat(apiDomain.contactDetails.admin.zip),\n                                    company: apiDomain.contactDetails.admin.company,\n                                    contactId: apiDomain.contactDetails.admin.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                technical: ((_apiDomain_contactDetails2 = apiDomain.contactDetails) === null || _apiDomain_contactDetails2 === void 0 ? void 0 : _apiDomain_contactDetails2.tech) ? {\n                                    name: apiDomain.contactDetails.tech.name,\n                                    email: apiDomain.contactDetails.tech.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.tech.telnocc, \" \").concat(apiDomain.contactDetails.tech.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.tech.address1, \", \").concat(apiDomain.contactDetails.tech.city, \", \").concat(apiDomain.contactDetails.tech.country, \" \").concat(apiDomain.contactDetails.tech.zip),\n                                    company: apiDomain.contactDetails.tech.company,\n                                    contactId: apiDomain.contactDetails.tech.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                billing: ((_apiDomain_contactDetails3 = apiDomain.contactDetails) === null || _apiDomain_contactDetails3 === void 0 ? void 0 : _apiDomain_contactDetails3.billing) ? {\n                                    name: apiDomain.contactDetails.billing.name,\n                                    email: apiDomain.contactDetails.billing.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.billing.telnocc, \" \").concat(apiDomain.contactDetails.billing.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.billing.address1, \", \").concat(apiDomain.contactDetails.billing.city, \", \").concat(apiDomain.contactDetails.billing.country, \" \").concat(apiDomain.contactDetails.billing.zip),\n                                    company: apiDomain.contactDetails.billing.company,\n                                    contactId: apiDomain.contactDetails.billing.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                }\n                            },\n                            // Contact IDs for API operations\n                            contactIds: apiDomain.contacts,\n                            // Additional real data from API\n                            productCategory: apiDomain.productCategory,\n                            productKey: apiDomain.productKey,\n                            customerId: apiDomain.customerId,\n                            gdpr: apiDomain.gdpr,\n                            locks: apiDomain.locks,\n                            raaVerification: apiDomain.raaVerification,\n                            dnssec: apiDomain.dnssec,\n                            // Raw API response for debugging\n                            apiDetails: apiDomain,\n                            // Default DNS records (placeholder - would need separate API call)\n                            dnsRecords: [\n                                {\n                                    id: \"rec1\",\n                                    type: \"A\",\n                                    name: \"@\",\n                                    content: \"DNS records available via separate API\",\n                                    ttl: 3600\n                                }\n                            ]\n                        };\n                        setDomain(combinedDomain);\n                    } else {\n                        throw new Error(\"No domain data received from API\");\n                    }\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"ns1.ztech\",\n                            \"ns2.ztech\",\n                            \"ns3.ztech\",\n                            \"ns4.ztech\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            technical: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] Toggling privacy protection for domain \".concat(domain.name, \":\"), {\n                from: domain.privacyProtection,\n                to: value,\n                orderId: domain.orderId\n            });\n            if (!domain.orderId) {\n                console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ No order ID available for domain\");\n                return;\n            }\n            // Call the modify privacy protection API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].modifyPrivacyProtection({\n                orderId: domain.orderId,\n                protectPrivacy: value,\n                reason: \"User \".concat(value ? \"enabled\" : \"disabled\", \" privacy protection via domain management panel\")\n            });\n            console.log(\"\\uD83D\\uDD27 [PRIVACY] ✅ Privacy protection updated successfully:\", response.data);\n            // Update local state\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n            // Show success message\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Privacy protection \".concat(value ? \"enabled\" : \"disabled\", \" successfully\"));\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD27 [PRIVACY] ❌ Error toggling privacy protection:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to update privacy protection. Please try again.\");\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(((_domain_status = domain.status) === null || _domain_status === void 0 ? void 0 : _domain_status.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status1 = domain.status) === null || _domain_status1 === void 0 ? void 0 : _domain_status1.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status2 = domain.status) === null || _domain_status2 === void 0 ? void 0 : _domain_status2.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status3 = domain.status) === null || _domain_status3 === void 0 ? void 0 : _domain_status3.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(((_domain_status4 = domain.status) === null || _domain_status4 === void 0 ? void 0 : _domain_status4.toLowerCase()) || \"unknown\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        t(\"visit_website\", {\n                                            defaultValue: \"Visit Website\"\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Order ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                domain.orderId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(((_domain_status5 = domain.status) === null || _domain_status5 === void 0 ? void 0 : _domain_status5.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status6 = domain.status) === null || _domain_status6 === void 0 ? void 0 : _domain_status6.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status7 = domain.status) === null || _domain_status7 === void 0 ? void 0 : _domain_status7.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status8 = domain.status) === null || _domain_status8 === void 0 ? void 0 : _domain_status8.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(((_domain_status9 = domain.status) === null || _domain_status9 === void 0 ? void 0 : _domain_status9.toLowerCase()) || \"unknown\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.registrationDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.expiryDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 535,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: \"Security & Protection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-end gap-2\",\n                                                                            children: ((_domain_privacyProtectionDetails = domain.privacyProtectionDetails) === null || _domain_privacyProtectionDetails === void 0 ? void 0 : _domain_privacyProtectionDetails.purchased) !== false ? // Privacy protection is purchased - show toggle\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        checked: domain.privacyProtection,\n                                                                                        onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                                        color: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 572,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 rounded \".concat(domain.privacyProtection ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                                        children: domain.privacyProtection ? \"Enabled\" : \"Disabled\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 579,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 571,\n                                                                                columnNumber: 29\n                                                                            }, this) : // Privacy protection not purchased - show purchase option\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col items-end gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded\",\n                                                                                        children: \"Not Purchased\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 589,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        color: \"blue\",\n                                                                                        variant: \"outlined\",\n                                                                                        className: \"text-xs px-3 py-1\",\n                                                                                        onClick: ()=>handlePurchasePrivacy(),\n                                                                                        children: \"Purchase Privacy Protection\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 592,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderStatus && Array.isArray(domain.orderStatus) && domain.orderStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Domain Locks\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.orderStatus.map((lock, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded\",\n                                                                                    children: lock\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 613,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.domainStatus && Array.isArray(domain.domainStatus) && domain.domainStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registry Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.domainStatus.map((status, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                                                    children: status\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 628,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_raaVerification = domain.raaVerification) === null || _domain_raaVerification === void 0 ? void 0 : _domain_raaVerification.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"RAA Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.raaVerification.status === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerification.status === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerification.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 641,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_domain_gdpr = domain.gdpr) === null || _domain_gdpr === void 0 ? void 0 : _domain_gdpr.enabled) !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"GDPR Protection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.gdpr.enabled === \"true\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                            children: domain.gdpr.enabled === \"true\" ? \"Enabled\" : \"Disabled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                (((_domain_status10 = domain.status) === null || _domain_status10 === void 0 ? void 0 : _domain_status10.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" || domain.registrationError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registration Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 671,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-100 text-red-800 rounded\",\n                                                                                    children: \"Registration Failed\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 675,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                domain.registrationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-50 text-red-700 rounded\",\n                                                                                    children: domain.registrationError\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 679,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 674,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers && Array.isArray(domain.nameservers) && domain.nameservers.length > 0 ? domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 703,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 699,\n                                                                        columnNumber: 27\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_domain_status11 = domain.status) === null || _domain_status11 === void 0 ? void 0 : _domain_status11.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" ? \"Nameservers not available - Registration failed\" : \"No nameservers configured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: (updatedDomain)=>setDomain(updatedDomain)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: dt(\"manage_dns_records\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                                children: t(\"add_record\", {\n                                                                    defaultValue: \"Add Record\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"type\", {\n                                                                                    defaultValue: \"Type\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 763,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"name\", {\n                                                                                    defaultValue: \"Name\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"content\", {\n                                                                                    defaultValue: \"Content\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 769,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"ttl\", {\n                                                                                    defaultValue: \"TTL\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 772,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-500\",\n                                                                                children: t(\"actions\", {\n                                                                                    defaultValue: \"Actions\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 775,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                                    children: record.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 783,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 786,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.content\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.ttl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 792,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-right\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        variant: \"text\",\n                                                                                        className: \"text-blue-600\",\n                                                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/\").concat(record.id)),\n                                                                                        children: t(\"edit\", {\n                                                                                            defaultValue: \"Edit\"\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 796,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, record.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 782,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts = domain.contacts) === null || _domain_contacts === void 0 ? void 0 : _domain_contacts.registrant) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 835,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.registrant, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 839,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.registrant, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.registrant, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 853,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts1 = domain.contacts) === null || _domain_contacts1 === void 0 ? void 0 : _domain_contacts1.admin) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 872,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.admin, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 876,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.admin, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 880,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.admin, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 883,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.admin, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 886,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.admin, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.admin, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 890,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts2 = domain.contacts) === null || _domain_contacts2 === void 0 ? void 0 : _domain_contacts2.technical) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 909,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.technical, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 913,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.technical, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 917,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.technical, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 920,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.technical, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.technical, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.technical, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 927,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: hasContactData((_domain_contacts3 = domain.contacts) === null || _domain_contacts3 === void 0 ? void 0 : _domain_contacts3.billing) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"name\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"company\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-blue-600 font-medium\",\n                                                                                children: getContactInfo(domain.contacts.billing, \"company\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 952,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600 mt-2\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCE7 \",\n                                                                                    getContactInfo(domain.contacts.billing, \"email\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCDE \",\n                                                                                    getContactInfo(domain.contacts.billing, \"phone\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 959,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCCD \",\n                                                                                    getContactInfo(domain.contacts.billing, \"address\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            getContactInfo(domain.contacts.billing, \"contactId\", null) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                                children: [\n                                                                                    \"ID: \",\n                                                                                    getContactInfo(domain.contacts.billing, \"contactId\")\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 966,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                                        className: \"text-sm text-gray-500 italic\",\n                                                                        children: \"Contact information not available from reseller API\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 979,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 818,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 1002,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 1008,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 1000,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 993,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 443,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 381,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 380,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vY2xpZW50L2RvbWFpbnMvW2lkXS9wYWdlLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM0QztBQUNBO0FBQ0E7QUFDTDtBQVlMO0FBVVo7QUFDeUM7QUFDUTtBQUNjO0FBRXRFLFNBQVMwQixpQkFBaUIsS0FBVTtRQUFWLEVBQUVDLE1BQU0sRUFBRSxHQUFWO1FBcVgrRUMsZ0JBRWxHQSxpQkFFRUEsaUJBRUVBLGlCQUtKQSxpQkE2RnFHQSxpQkFFN0ZBLGlCQUVFQSxpQkFFRUEsaUJBS0pBLGlCQWlESEEsa0NBbUVKQSx5QkFnQkFBLGNBZUNBLGtCQTZDS0Esa0JBeUhXQSxrQkFxQ0FBLG1CQXFDQUEsbUJBdUNBQTs7SUFqNUJ0QyxNQUFNLEVBQUVDLEVBQUUsRUFBRSxHQUFHRjtJQUNmLE1BQU1HLElBQUk1QiwwREFBZUEsQ0FBQztJQUMxQixNQUFNNkIsS0FBSzdCLDBEQUFlQSxDQUFDO0lBQzNCLE1BQU0sQ0FBQzBCLFFBQVFJLFVBQVUsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ2dDLFNBQVNDLFdBQVcsR0FBR2pDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2tDLFdBQVdDLGFBQWEsR0FBR25DLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU1vQyxTQUFTbEMsMERBQVNBO0lBRXhCLDZDQUE2QztJQUM3QyxNQUFNbUMsYUFBYSxDQUFDQztRQUNsQixJQUFJLENBQUNBLGVBQWUsT0FBTztRQUMzQixJQUFJO1lBQ0YsTUFBTUMsT0FBTyxJQUFJQyxLQUFLQyxTQUFTSCxpQkFBaUI7WUFDaEQsT0FBT0MsS0FBS0csa0JBQWtCLENBQUMsU0FBUztnQkFDdENDLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLEtBQUs7Z0JBQ0xDLE1BQU07Z0JBQ05DLFFBQVE7WUFDVjtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkLE9BQU87UUFDVDtJQUNGO0lBRUEsMkNBQTJDO0lBQzNDLE1BQU1DLGVBQWUsQ0FBQ0M7UUFDcEIsSUFBSSxDQUFDQSxRQUFRLE9BQU87UUFDcEIsT0FBT0EsT0FBT0MsV0FBVyxHQUFHQyxPQUFPLENBQUMsbUJBQW1CO0lBQ3pEO0lBRUEscURBQXFEO0lBQ3JELE1BQU1DLGlCQUFpQixTQUFDQyxTQUFTQztZQUFPQyw0RUFBVztRQUNqRCxJQUFJLENBQUNGLFdBQVcsT0FBT0EsWUFBWSxVQUFVO1lBQzNDLE9BQU9FO1FBQ1Q7UUFDQSxPQUFPRixPQUFPLENBQUNDLE1BQU0sSUFBSUM7SUFDM0I7SUFFQSwyREFBMkQ7SUFDM0QsTUFBTUMsaUJBQWlCLENBQUNIO1FBQ3RCLE9BQU9BLFdBQVcsT0FBT0EsWUFBWSxZQUFhQSxDQUFBQSxRQUFRSSxJQUFJLElBQUlKLFFBQVFLLEtBQUs7SUFDakY7SUFFQTVELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTTZELG1CQUFtQjtZQUN2QixJQUFJO29CQUtrQkM7Z0JBSnBCNUIsV0FBVztnQkFFWCw4REFBOEQ7Z0JBQzlELE1BQU00QixhQUFhLE1BQU12QyxzRUFBZ0JBLENBQUN3QyxjQUFjO2dCQUN4RCxNQUFNQyxjQUFjRixFQUFBQSxtQkFBQUEsV0FBV0csSUFBSSxjQUFmSCx1Q0FBQUEsaUJBQWlCSSxPQUFPLEtBQUksRUFBRTtnQkFFbEQsdUNBQXVDO2dCQUN2QyxNQUFNQyxhQUFhSCxZQUFZSSxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRXhDLEVBQUUsS0FBS0E7Z0JBRXBELElBQUksQ0FBQ3NDLFlBQVk7b0JBQ2ZHLFFBQVFyQixLQUFLLENBQUMsNkJBQTZCcEI7b0JBQzNDSyxXQUFXO29CQUNYO2dCQUNGO2dCQUVBb0MsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQko7Z0JBRWxDLHdEQUF3RDtnQkFDeEQsSUFBSTt3QkFVZ0JLLGtCQU1HQyw4QkFDSEE7b0JBaEJsQkgsUUFBUUMsR0FBRyxDQUFDLGtEQUF3Q0osV0FBV1IsSUFBSTtvQkFFbkUsNENBQTRDO29CQUM1QyxNQUFNYSxhQUFhLE1BQU1qRCxzRUFBZ0JBLENBQUNtRCxzQkFBc0IsQ0FDOURQLFdBQVdSLElBQUksRUFDZixNQUFNLDRCQUE0Qjs7b0JBRXBDVyxRQUFRQyxHQUFHLENBQUMsNENBQTRDQyxXQUFXUCxJQUFJO29CQUV2RSxNQUFNUSxhQUFZRCxtQkFBQUEsV0FBV1AsSUFBSSxjQUFmTyx1Q0FBQUEsaUJBQWlCNUMsTUFBTTtvQkFFekMsZ0NBQWdDO29CQUNoQzBDLFFBQVFDLEdBQUcsQ0FBQyxrREFBd0M7d0JBQ2xESSxvQkFBb0IsRUFBRUYsc0JBQUFBLGdDQUFBQSxVQUFXRyxpQkFBaUI7d0JBQ2xEQyx1QkFBdUJWLFdBQVdTLGlCQUFpQjt3QkFDbkRFLGlCQUFpQixFQUFFTCxzQkFBQUEsaUNBQUFBLCtCQUFBQSxVQUFXRyxpQkFBaUIsY0FBNUJILG1EQUFBQSw2QkFBOEJNLE9BQU87d0JBQ3hEQyxnQkFBZ0JQLENBQUFBLHNCQUFBQSxpQ0FBQUEsZ0NBQUFBLFVBQVdHLGlCQUFpQixjQUE1Qkgsb0RBQUFBLDhCQUE4Qk0sT0FBTyxNQUFLRTtvQkFDNUQ7b0JBRUEsSUFBSVIsV0FBVzs0QkFzQlFBLCtCQVlMQSwyQkFhTEEsNEJBYUlBLDRCQWFGQTt3QkF4RWIsa0NBQWtDO3dCQUNsQyxNQUFNUyxpQkFBaUI7NEJBQ3JCckQsSUFBSXNDLFdBQVd0QyxFQUFFOzRCQUNqQjhCLE1BQU1jLFVBQVVVLFVBQVUsSUFBSWhCLFdBQVdSLElBQUk7NEJBQzdDUixRQUFRc0IsVUFBVXRCLE1BQU0sSUFBSWdCLFdBQVdoQixNQUFNOzRCQUM3Q2lDLGtCQUFrQlgsVUFBVVcsZ0JBQWdCLElBQUlqQixXQUFXaUIsZ0JBQWdCOzRCQUMzRUMsWUFBWVosVUFBVVksVUFBVSxJQUFJbEIsV0FBV2tCLFVBQVU7NEJBQ3pEQyxXQUFXYixVQUFVYSxTQUFTLElBQUluQixXQUFXbUIsU0FBUyxJQUFJOzRCQUMxREMsV0FBVzs0QkFFWCxnQ0FBZ0M7NEJBQ2hDQyxhQUFhZixVQUFVZSxXQUFXLElBQUlmLFVBQVVlLFdBQVcsQ0FBQ0MsTUFBTSxHQUFHLElBQ2pFaEIsVUFBVWUsV0FBVyxHQUNyQnJCLFdBQVdxQixXQUFXLElBQUk7Z0NBQzFCO2dDQUNBO2dDQUNBO2dDQUNBOzZCQUNEOzRCQUVILDJFQUEyRTs0QkFDM0VaLG1CQUFtQkgsRUFBQUEsZ0NBQUFBLFVBQVVHLGlCQUFpQixjQUEzQkgsb0RBQUFBLDhCQUE2Qk0sT0FBTyxNQUFLRSxZQUN4RFIsVUFBVUcsaUJBQWlCLENBQUNHLE9BQU8sR0FDbENaLFdBQVdTLGlCQUFpQixJQUFJOzRCQUNyQ2MsMEJBQTBCakIsVUFBVUcsaUJBQWlCOzRCQUVyRGUsUUFBUXhCLFdBQVd3QixNQUFNOzRCQUN6QkMsT0FBT3pCLFdBQVd5QixLQUFLOzRCQUN2QkMsU0FBU3BCLFVBQVVvQixPQUFPLElBQUkxQixXQUFXMEIsT0FBTzs0QkFDaERDLGFBQWFyQixVQUFVcUIsV0FBVyxJQUFJM0IsV0FBVzJCLFdBQVc7NEJBRTVELGdDQUFnQzs0QkFDaENDLFVBQVU7Z0NBQ1JDLFlBQVl2QixFQUFBQSw0QkFBQUEsVUFBVXdCLGNBQWMsY0FBeEJ4QixnREFBQUEsMEJBQTBCdUIsVUFBVSxJQUFHO29DQUNqRHJDLE1BQU1jLFVBQVV3QixjQUFjLENBQUNELFVBQVUsQ0FBQ3JDLElBQUk7b0NBQzlDQyxPQUFPYSxVQUFVd0IsY0FBYyxDQUFDRCxVQUFVLENBQUNFLFNBQVM7b0NBQ3BEQyxPQUFPLElBQW1EMUIsT0FBL0NBLFVBQVV3QixjQUFjLENBQUNELFVBQVUsQ0FBQ0ksT0FBTyxFQUFDLEtBQTZDLE9BQTFDM0IsVUFBVXdCLGNBQWMsQ0FBQ0QsVUFBVSxDQUFDSyxLQUFLO29DQUNuR0MsU0FBUyxHQUFvRDdCLE9BQWpEQSxVQUFVd0IsY0FBYyxDQUFDRCxVQUFVLENBQUNPLFFBQVEsRUFBQyxNQUFpRDlCLE9BQTdDQSxVQUFVd0IsY0FBYyxDQUFDRCxVQUFVLENBQUNRLElBQUksRUFBQyxNQUFtRC9CLE9BQS9DQSxVQUFVd0IsY0FBYyxDQUFDRCxVQUFVLENBQUNTLE9BQU8sRUFBQyxLQUEyQyxPQUF4Q2hDLFVBQVV3QixjQUFjLENBQUNELFVBQVUsQ0FBQ1UsR0FBRztvQ0FDaE1DLFNBQVNsQyxVQUFVd0IsY0FBYyxDQUFDRCxVQUFVLENBQUNXLE9BQU87b0NBQ3BEQyxXQUFXbkMsVUFBVXdCLGNBQWMsQ0FBQ0QsVUFBVSxDQUFDYSxTQUFTO2dDQUMxRCxJQUFJO29DQUNGbEQsTUFBTTtvQ0FDTkMsT0FBTztvQ0FDUHVDLE9BQU87b0NBQ1BHLFNBQVM7Z0NBQ1g7Z0NBQ0FRLE9BQU9yQyxFQUFBQSw2QkFBQUEsVUFBVXdCLGNBQWMsY0FBeEJ4QixpREFBQUEsMkJBQTBCcUMsS0FBSyxJQUFHO29DQUN2Q25ELE1BQU1jLFVBQVV3QixjQUFjLENBQUNhLEtBQUssQ0FBQ25ELElBQUk7b0NBQ3pDQyxPQUFPYSxVQUFVd0IsY0FBYyxDQUFDYSxLQUFLLENBQUNaLFNBQVM7b0NBQy9DQyxPQUFPLElBQThDMUIsT0FBMUNBLFVBQVV3QixjQUFjLENBQUNhLEtBQUssQ0FBQ1YsT0FBTyxFQUFDLEtBQXdDLE9BQXJDM0IsVUFBVXdCLGNBQWMsQ0FBQ2EsS0FBSyxDQUFDVCxLQUFLO29DQUN6RkMsU0FBUyxHQUErQzdCLE9BQTVDQSxVQUFVd0IsY0FBYyxDQUFDYSxLQUFLLENBQUNQLFFBQVEsRUFBQyxNQUE0QzlCLE9BQXhDQSxVQUFVd0IsY0FBYyxDQUFDYSxLQUFLLENBQUNOLElBQUksRUFBQyxNQUE4Qy9CLE9BQTFDQSxVQUFVd0IsY0FBYyxDQUFDYSxLQUFLLENBQUNMLE9BQU8sRUFBQyxLQUFzQyxPQUFuQ2hDLFVBQVV3QixjQUFjLENBQUNhLEtBQUssQ0FBQ0osR0FBRztvQ0FDNUtDLFNBQVNsQyxVQUFVd0IsY0FBYyxDQUFDYSxLQUFLLENBQUNILE9BQU87b0NBQy9DQyxXQUFXbkMsVUFBVXdCLGNBQWMsQ0FBQ2EsS0FBSyxDQUFDRCxTQUFTO2dDQUNyRCxJQUFJO29DQUNGbEQsTUFBTTtvQ0FDTkMsT0FBTztvQ0FDUHVDLE9BQU87b0NBQ1BHLFNBQVM7Z0NBQ1g7Z0NBQ0FTLFdBQVd0QyxFQUFBQSw2QkFBQUEsVUFBVXdCLGNBQWMsY0FBeEJ4QixpREFBQUEsMkJBQTBCdUMsSUFBSSxJQUFHO29DQUMxQ3JELE1BQU1jLFVBQVV3QixjQUFjLENBQUNlLElBQUksQ0FBQ3JELElBQUk7b0NBQ3hDQyxPQUFPYSxVQUFVd0IsY0FBYyxDQUFDZSxJQUFJLENBQUNkLFNBQVM7b0NBQzlDQyxPQUFPLElBQTZDMUIsT0FBekNBLFVBQVV3QixjQUFjLENBQUNlLElBQUksQ0FBQ1osT0FBTyxFQUFDLEtBQXVDLE9BQXBDM0IsVUFBVXdCLGNBQWMsQ0FBQ2UsSUFBSSxDQUFDWCxLQUFLO29DQUN2RkMsU0FBUyxHQUE4QzdCLE9BQTNDQSxVQUFVd0IsY0FBYyxDQUFDZSxJQUFJLENBQUNULFFBQVEsRUFBQyxNQUEyQzlCLE9BQXZDQSxVQUFVd0IsY0FBYyxDQUFDZSxJQUFJLENBQUNSLElBQUksRUFBQyxNQUE2Qy9CLE9BQXpDQSxVQUFVd0IsY0FBYyxDQUFDZSxJQUFJLENBQUNQLE9BQU8sRUFBQyxLQUFxQyxPQUFsQ2hDLFVBQVV3QixjQUFjLENBQUNlLElBQUksQ0FBQ04sR0FBRztvQ0FDeEtDLFNBQVNsQyxVQUFVd0IsY0FBYyxDQUFDZSxJQUFJLENBQUNMLE9BQU87b0NBQzlDQyxXQUFXbkMsVUFBVXdCLGNBQWMsQ0FBQ2UsSUFBSSxDQUFDSCxTQUFTO2dDQUNwRCxJQUFJO29DQUNGbEQsTUFBTTtvQ0FDTkMsT0FBTztvQ0FDUHVDLE9BQU87b0NBQ1BHLFNBQVM7Z0NBQ1g7Z0NBQ0FXLFNBQVN4QyxFQUFBQSw2QkFBQUEsVUFBVXdCLGNBQWMsY0FBeEJ4QixpREFBQUEsMkJBQTBCd0MsT0FBTyxJQUFHO29DQUMzQ3RELE1BQU1jLFVBQVV3QixjQUFjLENBQUNnQixPQUFPLENBQUN0RCxJQUFJO29DQUMzQ0MsT0FBT2EsVUFBVXdCLGNBQWMsQ0FBQ2dCLE9BQU8sQ0FBQ2YsU0FBUztvQ0FDakRDLE9BQU8sSUFBZ0QxQixPQUE1Q0EsVUFBVXdCLGNBQWMsQ0FBQ2dCLE9BQU8sQ0FBQ2IsT0FBTyxFQUFDLEtBQTBDLE9BQXZDM0IsVUFBVXdCLGNBQWMsQ0FBQ2dCLE9BQU8sQ0FBQ1osS0FBSztvQ0FDN0ZDLFNBQVMsR0FBaUQ3QixPQUE5Q0EsVUFBVXdCLGNBQWMsQ0FBQ2dCLE9BQU8sQ0FBQ1YsUUFBUSxFQUFDLE1BQThDOUIsT0FBMUNBLFVBQVV3QixjQUFjLENBQUNnQixPQUFPLENBQUNULElBQUksRUFBQyxNQUFnRC9CLE9BQTVDQSxVQUFVd0IsY0FBYyxDQUFDZ0IsT0FBTyxDQUFDUixPQUFPLEVBQUMsS0FBd0MsT0FBckNoQyxVQUFVd0IsY0FBYyxDQUFDZ0IsT0FBTyxDQUFDUCxHQUFHO29DQUNwTEMsU0FBU2xDLFVBQVV3QixjQUFjLENBQUNnQixPQUFPLENBQUNOLE9BQU87b0NBQ2pEQyxXQUFXbkMsVUFBVXdCLGNBQWMsQ0FBQ2dCLE9BQU8sQ0FBQ0osU0FBUztnQ0FDdkQsSUFBSTtvQ0FDRmxELE1BQU07b0NBQ05DLE9BQU87b0NBQ1B1QyxPQUFPO29DQUNQRyxTQUFTO2dDQUNYOzRCQUNGOzRCQUVBLGlDQUFpQzs0QkFDakNZLFlBQVl6QyxVQUFVc0IsUUFBUTs0QkFFOUIsZ0NBQWdDOzRCQUNoQ29CLGlCQUFpQjFDLFVBQVUwQyxlQUFlOzRCQUMxQ0MsWUFBWTNDLFVBQVUyQyxVQUFVOzRCQUNoQ0MsWUFBWTVDLFVBQVU0QyxVQUFVOzRCQUNoQ0MsTUFBTTdDLFVBQVU2QyxJQUFJOzRCQUNwQkMsT0FBTzlDLFVBQVU4QyxLQUFLOzRCQUN0QkMsaUJBQWlCL0MsVUFBVStDLGVBQWU7NEJBQzFDQyxRQUFRaEQsVUFBVWdELE1BQU07NEJBRXhCLGlDQUFpQzs0QkFDakNDLFlBQVlqRDs0QkFFWixtRUFBbUU7NEJBQ25Fa0QsWUFBWTtnQ0FDVjtvQ0FDRTlGLElBQUk7b0NBQ0orRixNQUFNO29DQUNOakUsTUFBTTtvQ0FDTmtFLFNBQVM7b0NBQ1RDLEtBQUs7Z0NBQ1A7NkJBQ0Q7d0JBQ0g7d0JBRUE5RixVQUFVa0Q7b0JBQ1osT0FBTzt3QkFDTCxNQUFNLElBQUk2QyxNQUFNO29CQUNsQjtnQkFDRixFQUFFLE9BQU9DLFVBQVU7b0JBQ2pCMUQsUUFBUTJELElBQUksQ0FBQyw0Q0FBNENEO29CQUV6RCxvQ0FBb0M7b0JBQ3BDLE1BQU1FLGlCQUFpQjt3QkFDckJyRyxJQUFJc0MsV0FBV3RDLEVBQUU7d0JBQ2pCOEIsTUFBTVEsV0FBV1IsSUFBSTt3QkFDckJSLFFBQVFnQixXQUFXaEIsTUFBTTt3QkFDekJpQyxrQkFBa0JqQixXQUFXaUIsZ0JBQWdCO3dCQUM3Q0MsWUFBWWxCLFdBQVdrQixVQUFVO3dCQUNqQ0MsV0FBV25CLFdBQVdtQixTQUFTO3dCQUMvQkMsV0FBV3BCLFdBQVdvQixTQUFTLElBQUk7d0JBQ25DQyxhQUFhckIsV0FBV3FCLFdBQVcsSUFBSTs0QkFDckM7NEJBQ0E7NEJBQ0E7NEJBQ0E7eUJBQ0Q7d0JBQ0RaLG1CQUFtQlQsV0FBV1MsaUJBQWlCO3dCQUMvQ2UsUUFBUXhCLFdBQVd3QixNQUFNO3dCQUN6QkMsT0FBT3pCLFdBQVd5QixLQUFLO3dCQUN2QkMsU0FBUzFCLFdBQVcwQixPQUFPO3dCQUMzQkMsYUFBYTNCLFdBQVcyQixXQUFXO3dCQUNuQ0MsVUFBVTs0QkFDUkMsWUFBWTtnQ0FDVnJDLE1BQU07Z0NBQ05DLE9BQU87Z0NBQ1B1QyxPQUFPO2dDQUNQRyxTQUFTOzRCQUNYOzRCQUNBUSxPQUFPO2dDQUNMbkQsTUFBTTtnQ0FDTkMsT0FBTztnQ0FDUHVDLE9BQU87Z0NBQ1BHLFNBQVM7NEJBQ1g7NEJBQ0FTLFdBQVc7Z0NBQ1RwRCxNQUFNO2dDQUNOQyxPQUFPO2dDQUNQdUMsT0FBTztnQ0FDUEcsU0FBUzs0QkFDWDt3QkFDRjt3QkFDQXFCLFlBQVk7NEJBQ1Y7Z0NBQ0U5RixJQUFJO2dDQUNKK0YsTUFBTTtnQ0FDTmpFLE1BQU07Z0NBQ05rRSxTQUFTO2dDQUNUQyxLQUFLOzRCQUNQO3lCQUNEO29CQUNIO29CQUVBOUYsVUFBVWtHO2dCQUNaO2dCQUVBaEcsV0FBVztZQUNiLEVBQUUsT0FBT2UsT0FBTztnQkFDZHFCLFFBQVFyQixLQUFLLENBQUMsZ0NBQWdDQTtnQkFDOUNmLFdBQVc7WUFDYjtRQUNGO1FBQ0EyQjtJQUNGLEdBQUc7UUFBQ2hDO0tBQUc7SUFFUCxNQUFNc0csd0JBQXdCLE9BQU9DO1FBQ25DLElBQUk7WUFDRiwrREFBK0Q7WUFDL0QsdURBQXVEO1lBQ3ZEcEcsVUFBVTtnQkFBRSxHQUFHSixNQUFNO2dCQUFFMEQsV0FBVzhDO1lBQU07UUFDMUMsRUFBRSxPQUFPbkYsT0FBTztZQUNkcUIsUUFBUXJCLEtBQUssQ0FBQywrQkFBK0JBO1FBQy9DO0lBQ0Y7SUFFQSxNQUFNb0Ysc0JBQXNCLE9BQU9EO1FBQ2pDLElBQUk7WUFDRjlELFFBQVFDLEdBQUcsQ0FBQyxpRUFBbUUsT0FBWjNDLE9BQU8rQixJQUFJLEVBQUMsTUFBSTtnQkFDakYyRSxNQUFNMUcsT0FBT2dELGlCQUFpQjtnQkFDOUIyRCxJQUFJSDtnQkFDSnZDLFNBQVNqRSxPQUFPaUUsT0FBTztZQUN6QjtZQUVBLElBQUksQ0FBQ2pFLE9BQU9pRSxPQUFPLEVBQUU7Z0JBQ25CdkIsUUFBUXJCLEtBQUssQ0FBQztnQkFDZDtZQUNGO1lBRUEseUNBQXlDO1lBQ3pDLE1BQU11RixXQUFXLE1BQU1qSCxzRUFBZ0JBLENBQUNrSCx1QkFBdUIsQ0FBQztnQkFDOUQ1QyxTQUFTakUsT0FBT2lFLE9BQU87Z0JBQ3ZCNkMsZ0JBQWdCTjtnQkFDaEJPLFFBQVEsUUFBdUMsT0FBL0JQLFFBQVEsWUFBWSxZQUFXO1lBQ2pEO1lBRUE5RCxRQUFRQyxHQUFHLENBQUMscUVBQTJEaUUsU0FBU3ZFLElBQUk7WUFFcEYscUJBQXFCO1lBQ3JCakMsVUFBVTtnQkFBRSxHQUFHSixNQUFNO2dCQUFFZ0QsbUJBQW1Cd0Q7WUFBTTtZQUVoRCx1QkFBdUI7WUFDdkJoSSxpREFBS0EsQ0FBQ3dJLE9BQU8sQ0FBQyxzQkFBcUQsT0FBL0JSLFFBQVEsWUFBWSxZQUFXO1FBRXJFLEVBQUUsT0FBT25GLE9BQU87WUFDZHFCLFFBQVFyQixLQUFLLENBQUMsK0RBQXFEQTtZQUNuRTdDLGlEQUFLQSxDQUFDNkMsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLElBQUloQixTQUFTO1FBQ1gscUJBQ0UsOERBQUM0RztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDL0gsMElBQUtBOzRCQUFDK0gsV0FBVTs7Ozs7Ozs7Ozs7a0NBRW5CLDhEQUFDekksZ0VBQVVBO3dCQUFDMEksU0FBUTt3QkFBS0QsV0FBVTs7NEJBQ2hDaEgsRUFBRTs0QkFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3hCO0lBRUEsSUFBSSxDQUFDRixRQUFRO1FBQ1gscUJBQ0UsOERBQUNpSDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ3pJLGdFQUFVQTtvQkFBQzBJLFNBQVE7b0JBQUtELFdBQVU7OEJBQ2hDaEgsRUFBRSxvQkFBb0I7d0JBQUVrSCxjQUFjO29CQUFtQjs7Ozs7OzhCQUU1RCw4REFBQ3hJLDREQUFNQTtvQkFDTHNJLFdBQVU7b0JBQ1ZHLFNBQVMsSUFBTTVHLE9BQU82RyxJQUFJLENBQUM7O3NDQUUzQiw4REFBQ2xJLDJJQUFTQTs0QkFBQzhILFdBQVU7Ozs7Ozt3QkFDcEIvRyxHQUFHOzs7Ozs7Ozs7Ozs7O0lBSVo7SUFFQSxxQkFDRSw4REFBQzhHO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDdEksNERBQU1BO29CQUNMdUksU0FBUTtvQkFDUkQsV0FBVTtvQkFDVkcsU0FBUyxJQUFNNUcsT0FBTzZHLElBQUksQ0FBQzs7c0NBRTNCLDhEQUFDbEksMklBQVNBOzRCQUFDOEgsV0FBVTs7Ozs7O3dCQUNwQi9HLEdBQUc7Ozs7Ozs7OEJBR04sOERBQUM4RztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUMvSCwwSUFBS0E7d0NBQUMrSCxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFbkIsOERBQUNEOztzREFDQyw4REFBQ3hJLGdFQUFVQTs0Q0FDVDBJLFNBQVE7NENBQ1JELFdBQVU7c0RBRVRsSCxPQUFPK0IsSUFBSTs7Ozs7O3NEQUVkLDhEQUFDa0Y7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDSztvREFDQ0wsV0FBVywyRkFTUixPQVRtR2xILEVBQUFBLGlCQUFBQSxPQUFPdUIsTUFBTSxjQUFidkIscUNBQUFBLGVBQWV3QixXQUFXLFFBQU8sV0FDbkksZ0NBQ0F4QixFQUFBQSxrQkFBQUEsT0FBT3VCLE1BQU0sY0FBYnZCLHNDQUFBQSxnQkFBZXdCLFdBQVcsUUFBTyxZQUMvQixrQ0FDQXhCLEVBQUFBLGtCQUFBQSxPQUFPdUIsTUFBTSxjQUFidkIsc0NBQUFBLGdCQUFld0IsV0FBVyxRQUFPLFlBQy9CLDRCQUNBeEIsRUFBQUEsa0JBQUFBLE9BQU91QixNQUFNLGNBQWJ2QixzQ0FBQUEsZ0JBQWV3QixXQUFXLFFBQU8sV0FDL0IsNEJBQ0E7OERBR1RyQixHQUFHSCxFQUFBQSxrQkFBQUEsT0FBT3VCLE1BQU0sY0FBYnZCLHNDQUFBQSxnQkFBZXdCLFdBQVcsT0FBTTs7Ozs7OzhEQUV0Qyw4REFBQy9DLGdFQUFVQTtvREFBQ3lJLFdBQVU7O3dEQUNuQi9HLEdBQUc7d0RBQWE7d0RBQUdILE9BQU8yRCxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUs1Qyw4REFBQ3NEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3RJLDREQUFNQTtvQ0FDTHVJLFNBQVE7b0NBQ1JELFdBQVU7b0NBQ1ZHLFNBQVMsSUFBTUcsT0FBT0MsSUFBSSxDQUFDLFVBQXNCLE9BQVp6SCxPQUFPK0IsSUFBSSxHQUFJOzt3Q0FFbkQ3QixFQUFFLGlCQUFpQjs0Q0FBRWtILGNBQWM7d0NBQWdCO3NEQUNwRCw4REFBQzNILDJJQUFZQTs0Q0FBQ3lILFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFMUIsOERBQUN0SSw0REFBTUE7b0NBQ0xzSSxXQUFVO29DQUNWRyxTQUFTLElBQU01RyxPQUFPNkcsSUFBSSxDQUFDLG1CQUFzQixPQUFIckgsSUFBRzs7d0NBRWhERSxHQUFHO3NEQUNKLDhEQUFDVCwySUFBU0E7NENBQUN3SCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSzNCLDhEQUFDckksMERBQUlBO29CQUFDMkgsT0FBT2pHO29CQUFXMkcsV0FBVTs7c0NBQ2hDLDhEQUFDcEksZ0VBQVVBOzRCQUNUb0ksV0FBVTs0QkFDVlEsZ0JBQWdCO2dDQUNkUixXQUFXOzRCQUNiOzs4Q0FFQSw4REFBQ2xJLHlEQUFHQTtvQ0FDRndILE9BQU07b0NBQ05hLFNBQVMsSUFBTTdHLGFBQWE7b0NBQzVCMEcsV0FBVzNHLGNBQWMsYUFBYSxrQkFBa0I7OENBRXZETCxFQUFFLFlBQVk7d0NBQUVrSCxjQUFjO29DQUFXOzs7Ozs7OENBRTVDLDhEQUFDcEkseURBQUdBO29DQUNGd0gsT0FBTTtvQ0FDTmEsU0FBUyxJQUFNN0csYUFBYTtvQ0FDNUIwRyxXQUFXM0csY0FBYyxRQUFRLGtCQUFrQjs4Q0FFbERKLEdBQUc7Ozs7Ozs4Q0FFTiw4REFBQ25CLHlEQUFHQTtvQ0FDRndILE9BQU07b0NBQ05hLFNBQVMsSUFBTTdHLGFBQWE7b0NBQzVCMEcsV0FBVzNHLGNBQWMsYUFBYSxrQkFBa0I7OENBRXZESixHQUFHOzs7Ozs7OENBRU4sOERBQUNuQix5REFBR0E7b0NBQ0Z3SCxPQUFNO29DQUNOYSxTQUFTLElBQU03RyxhQUFhO29DQUM1QjBHLFdBQVczRyxjQUFjLFlBQVksa0JBQWtCOzhDQUV0REwsRUFBRSxXQUFXO3dDQUFFa0gsY0FBYztvQ0FBVTs7Ozs7Ozs7Ozs7O3NDQUc1Qyw4REFBQ3JJLDhEQUFRQTs7OENBQ1AsOERBQUNFLDhEQUFRQTtvQ0FBQ3VILE9BQU07b0NBQVdVLFdBQVU7OENBQ25DLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN4SSwwREFBSUE7Z0RBQUN3SSxXQUFVOzBEQUNkLDRFQUFDdkksOERBQVFBO29EQUFDdUksV0FBVTs7c0VBQ2xCLDhEQUFDekksZ0VBQVVBOzREQUFDeUksV0FBVTtzRUFDbkIvRyxHQUFHOzs7Ozs7c0VBRU4sOERBQUM4Rzs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3pJLGdFQUFVQTs0RUFBQ3lJLFdBQVU7c0ZBQ25CL0csR0FBRzs7Ozs7O3NGQUVOLDhEQUFDMUIsZ0VBQVVBOzRFQUFDeUksV0FBVTtzRkFDbkJsSCxPQUFPK0IsSUFBSTs7Ozs7Ozs7Ozs7OzhFQUdoQiw4REFBQ2tGO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3pJLGdFQUFVQTs0RUFBQ3lJLFdBQVU7c0ZBQXdCOzs7Ozs7c0ZBRzlDLDhEQUFDekksZ0VBQVVBOzRFQUFDeUksV0FBVTs7Z0ZBQTRCO2dGQUM5Q2xILE9BQU9pRSxPQUFPOzs7Ozs7Ozs7Ozs7OzhFQUdwQiw4REFBQ2dEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3pJLGdFQUFVQTs0RUFBQ3lJLFdBQVU7c0ZBQ25CL0csR0FBRzs7Ozs7O3NGQUVOLDhEQUFDb0g7NEVBQ0NMLFdBQVcsc0ZBU1IsT0FUOEZsSCxFQUFBQSxrQkFBQUEsT0FBT3VCLE1BQU0sY0FBYnZCLHNDQUFBQSxnQkFBZXdCLFdBQVcsUUFBTyxXQUM5SCxnQ0FDQXhCLEVBQUFBLGtCQUFBQSxPQUFPdUIsTUFBTSxjQUFidkIsc0NBQUFBLGdCQUFld0IsV0FBVyxRQUFPLFlBQy9CLGtDQUNBeEIsRUFBQUEsa0JBQUFBLE9BQU91QixNQUFNLGNBQWJ2QixzQ0FBQUEsZ0JBQWV3QixXQUFXLFFBQU8sWUFDL0IsNEJBQ0F4QixFQUFBQSxrQkFBQUEsT0FBT3VCLE1BQU0sY0FBYnZCLHNDQUFBQSxnQkFBZXdCLFdBQVcsUUFBTyxXQUMvQiw0QkFDQTtzRkFHVHJCLEdBQUdILEVBQUFBLGtCQUFBQSxPQUFPdUIsTUFBTSxjQUFidkIsc0NBQUFBLGdCQUFld0IsV0FBVyxPQUFNOzs7Ozs7Ozs7Ozs7OEVBR3hDLDhEQUFDeUY7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDekksZ0VBQVVBOzRFQUFDeUksV0FBVTtzRkFDbkIvRyxHQUFHOzs7Ozs7c0ZBRU4sOERBQUMxQixnRUFBVUE7NEVBQUN5SSxXQUFVO3NGQUNuQnhHLFdBQVdWLE9BQU93RCxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs4RUFHdkMsOERBQUN5RDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUN6SSxnRUFBVUE7NEVBQUN5SSxXQUFVO3NGQUNuQi9HLEdBQUc7Ozs7OztzRkFFTiw4REFBQzFCLGdFQUFVQTs0RUFBQ3lJLFdBQVU7c0ZBQ25CeEcsV0FBV1YsT0FBT3lELFVBQVU7Ozs7Ozs7Ozs7Ozs4RUFHakMsOERBQUN3RDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUN6SSxnRUFBVUE7NEVBQUN5SSxXQUFVO3NGQUNuQi9HLEdBQUc7Ozs7OztzRkFFTiw4REFBQ2pCLDREQUFNQTs0RUFDTHlJLFNBQVMzSCxPQUFPMEQsU0FBUzs0RUFDekJrRSxVQUFVLENBQUNDLElBQ1R0QixzQkFBc0JzQixFQUFFQyxNQUFNLENBQUNILE9BQU87NEVBRXhDSSxPQUFNOzRFQUNOQyxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFRcEIsOERBQUN0SiwwREFBSUE7Z0RBQUN3SSxXQUFVOzBEQUNkLDRFQUFDdkksOERBQVFBO29EQUFDdUksV0FBVTs7c0VBQ2xCLDhEQUFDekksZ0VBQVVBOzREQUFDeUksV0FBVTtzRUFBeUM7Ozs7OztzRUFHL0QsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDekksZ0VBQVVBOzRFQUFDeUksV0FBVTtzRkFDbkIvRyxHQUFHOzs7Ozs7c0ZBRU4sOERBQUM4Rzs0RUFBSUMsV0FBVTtzRkFFWmxILEVBQUFBLG1DQUFBQSxPQUFPOEQsd0JBQXdCLGNBQS9COUQsdURBQUFBLGlDQUFpQ2lJLFNBQVMsTUFBSyxRQUM5QyxnREFBZ0Q7MEZBQ2hELDhEQUFDaEI7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDaEksNERBQU1BO3dGQUNMeUksU0FBUzNILE9BQU9nRCxpQkFBaUI7d0ZBQ2pDNEUsVUFBVSxDQUFDQyxJQUNUcEIsb0JBQW9Cb0IsRUFBRUMsTUFBTSxDQUFDSCxPQUFPO3dGQUV0Q0ksT0FBTTs7Ozs7O2tHQUVSLDhEQUFDUjt3RkFBS0wsV0FBVyw2QkFHZCxPQUgyQ2xILE9BQU9nRCxpQkFBaUIsR0FDaEUsZ0NBQ0E7a0dBRUhoRCxPQUFPZ0QsaUJBQWlCLEdBQUcsWUFBWTs7Ozs7Ozs7Ozs7dUZBSTVDLDBEQUEwRDswRkFDMUQsOERBQUNpRTtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNLO3dGQUFLTCxXQUFVO2tHQUEwRDs7Ozs7O2tHQUcxRSw4REFBQ3RJLDREQUFNQTt3RkFDTHNKLE1BQUs7d0ZBQ0xILE9BQU07d0ZBQ05aLFNBQVE7d0ZBQ1JELFdBQVU7d0ZBQ1ZHLFNBQVMsSUFBTWM7a0dBQ2hCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnRUFRUm5JLE9BQU9rRSxXQUFXLElBQUlrRSxNQUFNQyxPQUFPLENBQUNySSxPQUFPa0UsV0FBVyxLQUFLbEUsT0FBT2tFLFdBQVcsQ0FBQ0wsTUFBTSxHQUFHLG1CQUN0Riw4REFBQ29EO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3pJLGdFQUFVQTs0RUFBQ3lJLFdBQVU7c0ZBQXdCOzs7Ozs7c0ZBRzlDLDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFDWmxILE9BQU9rRSxXQUFXLENBQUNvRSxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQzdCLDhEQUFDakI7b0ZBQWlCTCxXQUFVOzhGQUN6QnFCO21GQURRQzs7Ozs7Ozs7Ozs7Ozs7OztnRUFRbEJ4SSxPQUFPeUksWUFBWSxJQUFJTCxNQUFNQyxPQUFPLENBQUNySSxPQUFPeUksWUFBWSxLQUFLekksT0FBT3lJLFlBQVksQ0FBQzVFLE1BQU0sR0FBRyxtQkFDekYsOERBQUNvRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUN6SSxnRUFBVUE7NEVBQUN5SSxXQUFVO3NGQUF3Qjs7Ozs7O3NGQUc5Qyw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQ1psSCxPQUFPeUksWUFBWSxDQUFDSCxHQUFHLENBQUMsQ0FBQy9HLFFBQVFpSCxzQkFDaEMsOERBQUNqQjtvRkFBaUJMLFdBQVU7OEZBQ3pCM0Y7bUZBRFFpSDs7Ozs7Ozs7Ozs7Ozs7OztnRUFRbEJ4SSxFQUFBQSwwQkFBQUEsT0FBTzRGLGVBQWUsY0FBdEI1Riw4Q0FBQUEsd0JBQXdCdUIsTUFBTSxtQkFDN0IsOERBQUMwRjtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUN6SSxnRUFBVUE7NEVBQUN5SSxXQUFVO3NGQUF3Qjs7Ozs7O3NGQUc5Qyw4REFBQ0s7NEVBQUtMLFdBQVcsNkJBS2QsT0FMMkNsSCxPQUFPNEYsZUFBZSxDQUFDckUsTUFBTSxLQUFLLGFBQzVFLGdDQUNBdkIsT0FBTzRGLGVBQWUsQ0FBQ3JFLE1BQU0sS0FBSyxZQUNoQyxrQ0FDQTtzRkFFSHZCLE9BQU80RixlQUFlLENBQUNyRSxNQUFNOzs7Ozs7Ozs7Ozs7Z0VBS25DdkIsRUFBQUEsZUFBQUEsT0FBTzBGLElBQUksY0FBWDFGLG1DQUFBQSxhQUFhbUQsT0FBTyxNQUFLRSwyQkFDeEIsOERBQUM0RDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUN6SSxnRUFBVUE7NEVBQUN5SSxXQUFVO3NGQUF3Qjs7Ozs7O3NGQUc5Qyw4REFBQ0s7NEVBQUtMLFdBQVcsNkJBR2QsT0FIMkNsSCxPQUFPMEYsSUFBSSxDQUFDdkMsT0FBTyxLQUFLLFNBQ2xFLGdDQUNBO3NGQUVEbkQsT0FBTzBGLElBQUksQ0FBQ3ZDLE9BQU8sS0FBSyxTQUFTLFlBQVk7Ozs7Ozs7Ozs7OztnRUFNbERuRCxDQUFBQSxFQUFBQSxtQkFBQUEsT0FBT3VCLE1BQU0sY0FBYnZCLHVDQUFBQSxpQkFBZXdCLFdBQVcsUUFBTyxZQUNqQ3hCLE9BQU9rRSxXQUFXLEtBQUssWUFDdkJsRSxPQUFPMEksaUJBQWlCLG1CQUN0Qiw4REFBQ3pCO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ3pJLGdFQUFVQTs0RUFBQ3lJLFdBQVU7c0ZBQXdCOzs7Ozs7c0ZBRzlDLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNLO29GQUFLTCxXQUFVOzhGQUFvRDs7Ozs7O2dGQUduRWxILE9BQU8wSSxpQkFBaUIsa0JBQ3ZCLDhEQUFDbkI7b0ZBQUtMLFdBQVU7OEZBQ2JsSCxPQUFPMEksaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFXM0MsOERBQUNoSywwREFBSUE7Z0RBQUN3SSxXQUFVOzBEQUNkLDRFQUFDdkksOERBQVFBO29EQUFDdUksV0FBVTs7c0VBQ2xCLDhEQUFDekksZ0VBQVVBOzREQUFDeUksV0FBVTtzRUFDbkIvRyxHQUFHOzs7Ozs7c0VBRU4sOERBQUM4Rzs0REFBSUMsV0FBVTs7Z0VBQ1psSCxPQUFPNEQsV0FBVyxJQUFJd0UsTUFBTUMsT0FBTyxDQUFDckksT0FBTzRELFdBQVcsS0FBSzVELE9BQU80RCxXQUFXLENBQUNDLE1BQU0sR0FBRyxJQUN0RjdELE9BQU80RCxXQUFXLENBQUMwRSxHQUFHLENBQUMsQ0FBQ0ssSUFBSUgsc0JBQzFCLDhEQUFDdkI7d0VBRUNDLFdBQVU7OzBGQUVWLDhEQUFDekksZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQXdCO29GQUN4Q3NCLFFBQVE7Ozs7Ozs7MEZBRWQsOERBQUMvSixnRUFBVUE7Z0ZBQUN5SSxXQUFVOzBGQUFleUI7Ozs7Ozs7dUVBTmhDSDs7Ozs4RkFVVCw4REFBQ3ZCO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDekksZ0VBQVVBO3dFQUFDeUksV0FBVTtrRkFDbkJsSCxFQUFBQSxtQkFBQUEsT0FBT3VCLE1BQU0sY0FBYnZCLHVDQUFBQSxpQkFBZXdCLFdBQVcsUUFBTyxZQUFZeEIsT0FBT2tFLFdBQVcsS0FBSyxXQUNqRSxvREFDQTs7Ozs7Ozs7Ozs7OEVBS1YsOERBQUMrQztvRUFBSUMsV0FBVTs4RUFDYiw0RUFBQ3RJLDREQUFNQTt3RUFDTHVJLFNBQVE7d0VBQ1JELFdBQVU7d0VBQ1ZHLFNBQVMsSUFBTTdHLGFBQWE7a0ZBRTNCTCxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBU2xCLDhEQUFDbEIsOERBQVFBO29DQUFDdUgsT0FBTTtvQ0FBTVUsV0FBVTs7c0RBRTlCLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ3RILDZFQUFpQkE7Z0RBQ2hCSSxRQUFRQTtnREFDUjRJLFVBQVUsQ0FBQ0MsZ0JBQWtCekksVUFBVXlJOzs7Ozs7Ozs7OztzREFLM0MsOERBQUNuSywwREFBSUE7NENBQUN3SSxXQUFVO3NEQUNkLDRFQUFDdkksOERBQVFBO2dEQUFDdUksV0FBVTs7a0VBQ2xCLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN6SSxnRUFBVUE7Z0VBQUN5SSxXQUFVOzBFQUNuQi9HLEdBQUc7Ozs7OzswRUFFTiw4REFBQ3ZCLDREQUFNQTtnRUFDTHNJLFdBQVU7Z0VBQ1ZHLFNBQVMsSUFDUDVHLE9BQU82RyxJQUFJLENBQUMsbUJBQXNCLE9BQUhySCxJQUFHOzBFQUduQ0MsRUFBRSxjQUFjO29FQUFFa0gsY0FBYztnRUFBYTs7Ozs7Ozs7Ozs7O2tFQUdsRCw4REFBQ0g7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUM0Qjs0REFBTTVCLFdBQVU7OzhFQUNmLDhEQUFDNkI7OEVBQ0MsNEVBQUNDO3dFQUFHOUIsV0FBVTs7MEZBQ1osOERBQUMrQjtnRkFBRy9CLFdBQVU7MEZBQ1hoSCxFQUFFLFFBQVE7b0ZBQUVrSCxjQUFjO2dGQUFPOzs7Ozs7MEZBRXBDLDhEQUFDNkI7Z0ZBQUcvQixXQUFVOzBGQUNYaEgsRUFBRSxRQUFRO29GQUFFa0gsY0FBYztnRkFBTzs7Ozs7OzBGQUVwQyw4REFBQzZCO2dGQUFHL0IsV0FBVTswRkFDWGhILEVBQUUsV0FBVztvRkFBRWtILGNBQWM7Z0ZBQVU7Ozs7OzswRkFFMUMsOERBQUM2QjtnRkFBRy9CLFdBQVU7MEZBQ1hoSCxFQUFFLE9BQU87b0ZBQUVrSCxjQUFjO2dGQUFNOzs7Ozs7MEZBRWxDLDhEQUFDNkI7Z0ZBQUcvQixXQUFVOzBGQUNYaEgsRUFBRSxXQUFXO29GQUFFa0gsY0FBYztnRkFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBSTlDLDhEQUFDOEI7b0VBQU1oQyxXQUFVOzhFQUNkbEgsT0FBTytGLFVBQVUsQ0FBQ3VDLEdBQUcsQ0FBQyxDQUFDYSx1QkFDdEIsOERBQUNIOzRFQUFtQjlCLFdBQVU7OzhGQUM1Qiw4REFBQ2tDO29GQUFHbEMsV0FBVTs4RkFDWGlDLE9BQU9uRCxJQUFJOzs7Ozs7OEZBRWQsOERBQUNvRDtvRkFBR2xDLFdBQVU7OEZBQ1hpQyxPQUFPcEgsSUFBSTs7Ozs7OzhGQUVkLDhEQUFDcUg7b0ZBQUdsQyxXQUFVOzhGQUNYaUMsT0FBT2xELE9BQU87Ozs7Ozs4RkFFakIsOERBQUNtRDtvRkFBR2xDLFdBQVU7OEZBQ1hpQyxPQUFPakQsR0FBRzs7Ozs7OzhGQUViLDhEQUFDa0Q7b0ZBQUdsQyxXQUFVOzhGQUNaLDRFQUFDdEksNERBQU1BO3dGQUNMc0osTUFBSzt3RkFDTGYsU0FBUTt3RkFDUkQsV0FBVTt3RkFDVkcsU0FBUyxJQUNQNUcsT0FBTzZHLElBQUksQ0FDVCxtQkFBNkI2QixPQUFWbEosSUFBRyxTQUFpQixPQUFWa0osT0FBT2xKLEVBQUU7a0dBSXpDQyxFQUFFLFFBQVE7NEZBQUVrSCxjQUFjO3dGQUFPOzs7Ozs7Ozs7Ozs7MkVBeEIvQitCLE9BQU9sSixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FvQ2hDLDhEQUFDaEIsOERBQVFBO29DQUFDdUgsT0FBTTtvQ0FBV1UsV0FBVTs4Q0FFbkMsNEVBQUN4SSwwREFBSUE7d0NBQUN3SSxXQUFVO2tEQUNkLDRFQUFDdkksOERBQVFBOzRDQUFDdUksV0FBVTs7OERBQ2xCLDhEQUFDekksZ0VBQVVBO29EQUFDeUksV0FBVTs4REFDbkIvRyxHQUFHOzs7Ozs7OERBRU4sOERBQUM4RztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzs4RUFDQyw4REFBQ3hJLGdFQUFVQTtvRUFBQ3lJLFdBQVU7OEVBQ25CaEgsRUFBRSxjQUFjO3dFQUNma0gsY0FBYztvRUFDaEI7Ozs7Ozs4RUFFRiw4REFBQ0g7b0VBQUlDLFdBQVU7OEVBQ1pwRixnQkFBZTlCLG1CQUFBQSxPQUFPbUUsUUFBUSxjQUFmbkUsdUNBQUFBLGlCQUFpQm9FLFVBQVUsa0JBQ3pDOzswRkFDRSw4REFBQzNGLGdFQUFVQTtnRkFBQ3lJLFdBQVU7MEZBQ25CeEYsZUFBZTFCLE9BQU9tRSxRQUFRLENBQUNDLFVBQVUsRUFBRTs7Ozs7OzRFQUU3QzFDLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDQyxVQUFVLEVBQUUsV0FBVyx1QkFDckQsOERBQUMzRixnRUFBVUE7Z0ZBQUN5SSxXQUFVOzBGQUNuQnhGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDQyxVQUFVLEVBQUU7Ozs7OzswRkFHaEQsOERBQUMzRixnRUFBVUE7Z0ZBQUN5SSxXQUFVOztvRkFBNkI7b0ZBQzdDeEYsZUFBZTFCLE9BQU9tRSxRQUFRLENBQUNDLFVBQVUsRUFBRTs7Ozs7OzswRkFFakQsOERBQUMzRixnRUFBVUE7Z0ZBQUN5SSxXQUFVOztvRkFBd0I7b0ZBQ3hDeEYsZUFBZTFCLE9BQU9tRSxRQUFRLENBQUNDLFVBQVUsRUFBRTs7Ozs7OzswRkFFakQsOERBQUMzRixnRUFBVUE7Z0ZBQUN5SSxXQUFVOztvRkFBd0I7b0ZBQ3hDeEYsZUFBZTFCLE9BQU9tRSxRQUFRLENBQUNDLFVBQVUsRUFBRTs7Ozs7Ozs0RUFFaEQxQyxlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ0MsVUFBVSxFQUFFLGFBQWEsdUJBQ3ZELDhEQUFDM0YsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQTZCO29GQUM1Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDQyxVQUFVLEVBQUU7Ozs7Ozs7O3FHQUt0RCw4REFBQzNGLGdFQUFVQTt3RUFBQ3lJLFdBQVU7a0ZBQStCOzs7Ozs7Ozs7Ozs7Ozs7OztzRUFNM0QsOERBQUNEOzs4RUFDQyw4REFBQ3hJLGdFQUFVQTtvRUFBQ3lJLFdBQVU7OEVBQ25CaEgsRUFBRSxTQUFTO3dFQUFFa0gsY0FBYztvRUFBeUI7Ozs7Ozs4RUFFdkQsOERBQUNIO29FQUFJQyxXQUFVOzhFQUNacEYsZ0JBQWU5QixvQkFBQUEsT0FBT21FLFFBQVEsY0FBZm5FLHdDQUFBQSxrQkFBaUJrRixLQUFLLGtCQUNwQzs7MEZBQ0UsOERBQUN6RyxnRUFBVUE7Z0ZBQUN5SSxXQUFVOzBGQUNuQnhGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZSxLQUFLLEVBQUU7Ozs7Ozs0RUFFeEN4RCxlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2UsS0FBSyxFQUFFLFdBQVcsdUJBQ2hELDhEQUFDekcsZ0VBQVVBO2dGQUFDeUksV0FBVTswRkFDbkJ4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2UsS0FBSyxFQUFFOzs7Ozs7MEZBRzNDLDhEQUFDekcsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQTZCO29GQUM3Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZSxLQUFLLEVBQUU7Ozs7Ozs7MEZBRTVDLDhEQUFDekcsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQXdCO29GQUN4Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZSxLQUFLLEVBQUU7Ozs7Ozs7MEZBRTVDLDhEQUFDekcsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQXdCO29GQUN4Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZSxLQUFLLEVBQUU7Ozs7Ozs7NEVBRTNDeEQsZUFBZTFCLE9BQU9tRSxRQUFRLENBQUNlLEtBQUssRUFBRSxhQUFhLHVCQUNsRCw4REFBQ3pHLGdFQUFVQTtnRkFBQ3lJLFdBQVU7O29GQUE2QjtvRkFDNUN4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2UsS0FBSyxFQUFFOzs7Ozs7OztxR0FLakQsOERBQUN6RyxnRUFBVUE7d0VBQUN5SSxXQUFVO2tGQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBTTNELDhEQUFDRDs7OEVBQ0MsOERBQUN4SSxnRUFBVUE7b0VBQUN5SSxXQUFVOzhFQUNuQmhILEVBQUUsYUFBYTt3RUFBRWtILGNBQWM7b0VBQW9COzs7Ozs7OEVBRXRELDhEQUFDSDtvRUFBSUMsV0FBVTs4RUFDWnBGLGdCQUFlOUIsb0JBQUFBLE9BQU9tRSxRQUFRLGNBQWZuRSx3Q0FBQUEsa0JBQWlCbUYsU0FBUyxrQkFDeEM7OzBGQUNFLDhEQUFDMUcsZ0VBQVVBO2dGQUFDeUksV0FBVTswRkFDbkJ4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2dCLFNBQVMsRUFBRTs7Ozs7OzRFQUU1Q3pELGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZ0IsU0FBUyxFQUFFLFdBQVcsdUJBQ3BELDhEQUFDMUcsZ0VBQVVBO2dGQUFDeUksV0FBVTswRkFDbkJ4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2dCLFNBQVMsRUFBRTs7Ozs7OzBGQUcvQyw4REFBQzFHLGdFQUFVQTtnRkFBQ3lJLFdBQVU7O29GQUE2QjtvRkFDN0N4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2dCLFNBQVMsRUFBRTs7Ozs7OzswRkFFaEQsOERBQUMxRyxnRUFBVUE7Z0ZBQUN5SSxXQUFVOztvRkFBd0I7b0ZBQ3hDeEYsZUFBZTFCLE9BQU9tRSxRQUFRLENBQUNnQixTQUFTLEVBQUU7Ozs7Ozs7MEZBRWhELDhEQUFDMUcsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQXdCO29GQUN4Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZ0IsU0FBUyxFQUFFOzs7Ozs7OzRFQUUvQ3pELGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZ0IsU0FBUyxFQUFFLGFBQWEsdUJBQ3RELDhEQUFDMUcsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQTZCO29GQUM1Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDZ0IsU0FBUyxFQUFFOzs7Ozs7OztxR0FLckQsOERBQUMxRyxnRUFBVUE7d0VBQUN5SSxXQUFVO2tGQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBUTNELDhEQUFDRDs7OEVBQ0MsOERBQUN4SSxnRUFBVUE7b0VBQUN5SSxXQUFVOzhFQUNuQmhILEVBQUUsV0FBVzt3RUFBRWtILGNBQWM7b0VBQWtCOzs7Ozs7OEVBRWxELDhEQUFDSDtvRUFBSUMsV0FBVTs4RUFDWnBGLGdCQUFlOUIsb0JBQUFBLE9BQU9tRSxRQUFRLGNBQWZuRSx3Q0FBQUEsa0JBQWlCcUYsT0FBTyxrQkFDdEM7OzBGQUNFLDhEQUFDNUcsZ0VBQVVBO2dGQUFDeUksV0FBVTswRkFDbkJ4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2tCLE9BQU8sRUFBRTs7Ozs7OzRFQUUxQzNELGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDa0IsT0FBTyxFQUFFLFdBQVcsdUJBQ2xELDhEQUFDNUcsZ0VBQVVBO2dGQUFDeUksV0FBVTswRkFDbkJ4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2tCLE9BQU8sRUFBRTs7Ozs7OzBGQUc3Qyw4REFBQzVHLGdFQUFVQTtnRkFBQ3lJLFdBQVU7O29GQUE2QjtvRkFDN0N4RixlQUFlMUIsT0FBT21FLFFBQVEsQ0FBQ2tCLE9BQU8sRUFBRTs7Ozs7OzswRkFFOUMsOERBQUM1RyxnRUFBVUE7Z0ZBQUN5SSxXQUFVOztvRkFBd0I7b0ZBQ3hDeEYsZUFBZTFCLE9BQU9tRSxRQUFRLENBQUNrQixPQUFPLEVBQUU7Ozs7Ozs7MEZBRTlDLDhEQUFDNUcsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQXdCO29GQUN4Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDa0IsT0FBTyxFQUFFOzs7Ozs7OzRFQUU3QzNELGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDa0IsT0FBTyxFQUFFLGFBQWEsdUJBQ3BELDhEQUFDNUcsZ0VBQVVBO2dGQUFDeUksV0FBVTs7b0ZBQTZCO29GQUM1Q3hGLGVBQWUxQixPQUFPbUUsUUFBUSxDQUFDa0IsT0FBTyxFQUFFOzs7Ozs7OztxR0FLbkQsOERBQUM1RyxnRUFBVUE7d0VBQUN5SSxXQUFVO2tGQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTzdELDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ3RJLDREQUFNQTt3REFDTHNJLFdBQVU7d0RBQ1ZHLFNBQVMsSUFDUDVHLE9BQU82RyxJQUFJLENBQUMsbUJBQXNCLE9BQUhySCxJQUFHO2tFQUduQ0UsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU9kLDhEQUFDbEIsOERBQVFBO29DQUFDdUgsT0FBTTtvQ0FBVVUsV0FBVTs4Q0FFbEMsNEVBQUN4SSwwREFBSUE7d0NBQUN3SSxXQUFVO2tEQUNkLDRFQUFDdkksOERBQVFBOzRDQUFDdUksV0FBVTs7OERBQ2xCLDhEQUFDekksZ0VBQVVBO29EQUFDeUksV0FBVTs4REFDbkJoSCxFQUFFLFdBQVc7d0RBQUVrSCxjQUFjO29EQUFxQjs7Ozs7OzhEQUVyRCw4REFBQ0g7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDNUgsMklBQU1BOzREQUFDNEgsV0FBVTs7Ozs7O3NFQUNsQiw4REFBQ3pJLGdFQUFVQTs0REFBQ3lJLFdBQVU7c0VBQ25CaEgsRUFBRSwrQkFBK0I7Z0VBQ2hDa0gsY0FDRTs0REFDSjs7Ozs7O3NFQUVGLDhEQUFDM0ksZ0VBQVVBOzREQUFDeUksV0FBVTtzRUFDbkJoSCxFQUFFLHVCQUF1QjtnRUFDeEJrSCxjQUNFOzREQUNKOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXdEI7R0EvOUJ3QnRIOztRQUVaeEIsc0RBQWVBO1FBQ2RBLHNEQUFlQTtRQUlYQyxzREFBU0E7OztLQVBGdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9bbG9jYWxlXS9jbGllbnQvZG9tYWlucy9baWRdL3BhZ2UuanN4PzRmZWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSBcIm5leHQtaW50bFwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSBcInJlYWN0LXRvYXN0aWZ5XCI7XHJcbmltcG9ydCB7XHJcbiAgVHlwb2dyYXBoeSxcclxuICBDYXJkLFxyXG4gIENhcmRCb2R5LFxyXG4gIEJ1dHRvbixcclxuICBUYWJzLFxyXG4gIFRhYnNIZWFkZXIsXHJcbiAgVGFic0JvZHksXHJcbiAgVGFiLFxyXG4gIFRhYlBhbmVsLFxyXG4gIFN3aXRjaCxcclxufSBmcm9tIFwiQG1hdGVyaWFsLXRhaWx3aW5kL3JlYWN0XCI7XHJcbmltcG9ydCB7XHJcbiAgR2xvYmUsXHJcbiAgQXJyb3dMZWZ0LFxyXG4gIFNlcnZlcixcclxuICBTaGllbGQsXHJcbiAgTG9jayxcclxuICBNYWlsLFxyXG4gIEV4dGVybmFsTGluayxcclxuICBSZWZyZXNoQ3csXHJcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgZG9tYWluTW5nU2VydmljZSBmcm9tIFwiQC9hcHAvc2VydmljZXMvZG9tYWluTW5nU2VydmljZVwiO1xyXG5pbXBvcnQgTmFtZXNlcnZlck1hbmFnZXIgZnJvbSBcIkAvY29tcG9uZW50cy9kb21haW5zL05hbWVzZXJ2ZXJNYW5hZ2VyXCI7XHJcbmltcG9ydCBQcml2YWN5UHJvdGVjdGlvbk1hbmFnZXIgZnJvbSBcIkAvY29tcG9uZW50cy9kb21haW5zL1ByaXZhY3lQcm90ZWN0aW9uTWFuYWdlclwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9tYWluRGV0YWlsUGFnZSh7IHBhcmFtcyB9KSB7XHJcbiAgY29uc3QgeyBpZCB9ID0gcGFyYW1zO1xyXG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoXCJjbGllbnRcIik7XHJcbiAgY29uc3QgZHQgPSB1c2VUcmFuc2xhdGlvbnMoXCJjbGllbnQuZG9tYWluV3JhcHBlclwiKTtcclxuICBjb25zdCBbZG9tYWluLCBzZXREb21haW5dID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKFwib3ZlcnZpZXdcIik7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcblxyXG4gIC8vIFV0aWxpdHkgZnVuY3Rpb24gdG8gZm9ybWF0IFVuaXggdGltZXN0YW1wc1xyXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAodW5peFRpbWVzdGFtcCkgPT4ge1xyXG4gICAgaWYgKCF1bml4VGltZXN0YW1wKSByZXR1cm4gXCJOb3QgYXZhaWxhYmxlXCI7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUocGFyc2VJbnQodW5peFRpbWVzdGFtcCkgKiAxMDAwKTtcclxuICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKFwiZW4tVVNcIiwge1xyXG4gICAgICAgIHllYXI6IFwibnVtZXJpY1wiLFxyXG4gICAgICAgIG1vbnRoOiBcImxvbmdcIixcclxuICAgICAgICBkYXk6IFwibnVtZXJpY1wiLFxyXG4gICAgICAgIGhvdXI6IFwiMi1kaWdpdFwiLFxyXG4gICAgICAgIG1pbnV0ZTogXCIyLWRpZ2l0XCJcclxuICAgICAgfSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICByZXR1cm4gXCJJbnZhbGlkIGRhdGVcIjtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBVdGlsaXR5IGZ1bmN0aW9uIHRvIGZvcm1hdCBkb21haW4gc3RhdHVzXHJcbiAgY29uc3QgZm9ybWF0U3RhdHVzID0gKHN0YXR1cykgPT4ge1xyXG4gICAgaWYgKCFzdGF0dXMpIHJldHVybiBcInVua25vd25cIjtcclxuICAgIHJldHVybiBzdGF0dXMudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC8oW2Etel0pKFtBLVpdKS9nLCAnJDEgJDInKTtcclxuICB9O1xyXG5cclxuICAvLyBVdGlsaXR5IGZ1bmN0aW9uIHRvIHNhZmVseSBnZXQgY29udGFjdCBpbmZvcm1hdGlvblxyXG4gIGNvbnN0IGdldENvbnRhY3RJbmZvID0gKGNvbnRhY3QsIGZpZWxkLCBmYWxsYmFjayA9IFwiTm90IGF2YWlsYWJsZVwiKSA9PiB7XHJcbiAgICBpZiAoIWNvbnRhY3QgfHwgdHlwZW9mIGNvbnRhY3QgIT09ICdvYmplY3QnKSB7XHJcbiAgICAgIHJldHVybiBmYWxsYmFjaztcclxuICAgIH1cclxuICAgIHJldHVybiBjb250YWN0W2ZpZWxkXSB8fCBmYWxsYmFjaztcclxuICB9O1xyXG5cclxuICAvLyBVdGlsaXR5IGZ1bmN0aW9uIHRvIGNoZWNrIGlmIGNvbnRhY3QgZXhpc3RzIGFuZCBoYXMgZGF0YVxyXG4gIGNvbnN0IGhhc0NvbnRhY3REYXRhID0gKGNvbnRhY3QpID0+IHtcclxuICAgIHJldHVybiBjb250YWN0ICYmIHR5cGVvZiBjb250YWN0ID09PSAnb2JqZWN0JyAmJiAoY29udGFjdC5uYW1lIHx8IGNvbnRhY3QuZW1haWwpO1xyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBnZXREb21haW5EZXRhaWxzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcblxyXG4gICAgICAgIC8vIEZpcnN0LCBnZXQgdGhlIHVzZXIncyBkb21haW5zIHRvIGZpbmQgdGhlIGRvbWFpbiBuYW1lIGJ5IElEXHJcbiAgICAgICAgY29uc3QgZG9tYWluc1JlcyA9IGF3YWl0IGRvbWFpbk1uZ1NlcnZpY2UuZ2V0VXNlckRvbWFpbnMoKTtcclxuICAgICAgICBjb25zdCB1c2VyRG9tYWlucyA9IGRvbWFpbnNSZXMuZGF0YT8uZG9tYWlucyB8fCBbXTtcclxuXHJcbiAgICAgICAgLy8gRmluZCB0aGUgZG9tYWluIHdpdGggdGhlIG1hdGNoaW5nIElEXHJcbiAgICAgICAgY29uc3QgdXNlckRvbWFpbiA9IHVzZXJEb21haW5zLmZpbmQoKGQpID0+IGQuaWQgPT09IGlkKTtcclxuXHJcbiAgICAgICAgaWYgKCF1c2VyRG9tYWluKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRG9tYWluIG5vdCBmb3VuZCB3aXRoIElEOlwiLCBpZCk7XHJcbiAgICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiRm91bmQgdXNlciBkb21haW46XCIsIHVzZXJEb21haW4pO1xyXG5cclxuICAgICAgICAvLyBUcnkgdG8gZ2V0IGRldGFpbGVkIGluZm9ybWF0aW9uIGZyb20gdGhlIHJlc2VsbGVyIEFQSVxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gRmV0Y2hpbmcgcmVhbCBkb21haW4gZGV0YWlscyBmb3I6XCIsIHVzZXJEb21haW4ubmFtZSk7XHJcblxyXG4gICAgICAgICAgLy8gR2V0IHJlYWwgZG9tYWluIGRldGFpbHMgZnJvbSByZXNlbGxlciBBUElcclxuICAgICAgICAgIGNvbnN0IGRldGFpbHNSZXMgPSBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLmdldERvbWFpbkRldGFpbHNCeU5hbWUoXHJcbiAgICAgICAgICAgIHVzZXJEb21haW4ubmFtZSxcclxuICAgICAgICAgICAgXCJBbGxcIiAvLyBHZXQgYWxsIGF2YWlsYWJsZSBkZXRhaWxzXHJcbiAgICAgICAgICApO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coXCLinIUgUmVhbCBkb21haW4gZGV0YWlscyBmcm9tIHJlc2VsbGVyIEFQSTpcIiwgZGV0YWlsc1Jlcy5kYXRhKTtcclxuXHJcbiAgICAgICAgICBjb25zdCBhcGlEb21haW4gPSBkZXRhaWxzUmVzLmRhdGE/LmRvbWFpbjtcclxuXHJcbiAgICAgICAgICAvLyBEZWJ1ZyBwcml2YWN5IHByb3RlY3Rpb24gZGF0YVxyXG4gICAgICAgICAgY29uc29sZS5sb2coXCLwn5SSIFtQUklWQUNZIERFQlVHXSBBUEkgUHJpdmFjeSBEYXRhOlwiLCB7XHJcbiAgICAgICAgICAgIGFwaVByaXZhY3lQcm90ZWN0aW9uOiBhcGlEb21haW4/LnByaXZhY3lQcm90ZWN0aW9uLFxyXG4gICAgICAgICAgICB1c2VyUHJpdmFjeVByb3RlY3Rpb246IHVzZXJEb21haW4ucHJpdmFjeVByb3RlY3Rpb24sXHJcbiAgICAgICAgICAgIGFwaVByaXZhY3lFbmFibGVkOiBhcGlEb21haW4/LnByaXZhY3lQcm90ZWN0aW9uPy5lbmFibGVkLFxyXG4gICAgICAgICAgICB3aWxsVXNlQXBpRGF0YTogYXBpRG9tYWluPy5wcml2YWN5UHJvdGVjdGlvbj8uZW5hYmxlZCAhPT0gdW5kZWZpbmVkXHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICBpZiAoYXBpRG9tYWluKSB7XHJcbiAgICAgICAgICAgIC8vIFVzZSByZWFsIGRhdGEgZnJvbSByZXNlbGxlciBBUElcclxuICAgICAgICAgICAgY29uc3QgY29tYmluZWREb21haW4gPSB7XHJcbiAgICAgICAgICAgICAgaWQ6IHVzZXJEb21haW4uaWQsXHJcbiAgICAgICAgICAgICAgbmFtZTogYXBpRG9tYWluLmRvbWFpbk5hbWUgfHwgdXNlckRvbWFpbi5uYW1lLFxyXG4gICAgICAgICAgICAgIHN0YXR1czogYXBpRG9tYWluLnN0YXR1cyB8fCB1c2VyRG9tYWluLnN0YXR1cyxcclxuICAgICAgICAgICAgICByZWdpc3RyYXRpb25EYXRlOiBhcGlEb21haW4ucmVnaXN0cmF0aW9uRGF0ZSB8fCB1c2VyRG9tYWluLnJlZ2lzdHJhdGlvbkRhdGUsXHJcbiAgICAgICAgICAgICAgZXhwaXJ5RGF0ZTogYXBpRG9tYWluLmV4cGlyeURhdGUgfHwgdXNlckRvbWFpbi5leHBpcnlEYXRlLFxyXG4gICAgICAgICAgICAgIGF1dG9SZW5ldzogYXBpRG9tYWluLmF1dG9SZW5ldyB8fCB1c2VyRG9tYWluLmF1dG9SZW5ldyB8fCBmYWxzZSxcclxuICAgICAgICAgICAgICByZWdpc3RyYXI6IFwiWlRlY2ggRG9tYWluc1wiLFxyXG5cclxuICAgICAgICAgICAgICAvLyBVc2UgcmVhbCBuYW1lc2VydmVycyBmcm9tIEFQSVxyXG4gICAgICAgICAgICAgIG5hbWVzZXJ2ZXJzOiBhcGlEb21haW4ubmFtZXNlcnZlcnMgJiYgYXBpRG9tYWluLm5hbWVzZXJ2ZXJzLmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgICAgID8gYXBpRG9tYWluLm5hbWVzZXJ2ZXJzXHJcbiAgICAgICAgICAgICAgICA6IHVzZXJEb21haW4ubmFtZXNlcnZlcnMgfHwgW1xyXG4gICAgICAgICAgICAgICAgICBcIm5zMS56dGVjaFwiLFxyXG4gICAgICAgICAgICAgICAgICBcIm5zMi56dGVjaFwiLFxyXG4gICAgICAgICAgICAgICAgICBcIm5zMy56dGVjaFwiLFxyXG4gICAgICAgICAgICAgICAgICBcIm5zNC56dGVjaFwiXHJcbiAgICAgICAgICAgICAgICBdLFxyXG5cclxuICAgICAgICAgICAgICAvLyBVc2UgcmVhbCBwcml2YWN5IHByb3RlY3Rpb24gZGF0YSBmcm9tIHJlc2VsbGVyIEFQSSAocHJpb3JpdGl6ZSBBUEkgZGF0YSlcclxuICAgICAgICAgICAgICBwcml2YWN5UHJvdGVjdGlvbjogYXBpRG9tYWluLnByaXZhY3lQcm90ZWN0aW9uPy5lbmFibGVkICE9PSB1bmRlZmluZWRcclxuICAgICAgICAgICAgICAgID8gYXBpRG9tYWluLnByaXZhY3lQcm90ZWN0aW9uLmVuYWJsZWRcclxuICAgICAgICAgICAgICAgIDogKHVzZXJEb21haW4ucHJpdmFjeVByb3RlY3Rpb24gfHwgZmFsc2UpLFxyXG4gICAgICAgICAgICAgIHByaXZhY3lQcm90ZWN0aW9uRGV0YWlsczogYXBpRG9tYWluLnByaXZhY3lQcm90ZWN0aW9uLFxyXG5cclxuICAgICAgICAgICAgICBwZXJpb2Q6IHVzZXJEb21haW4ucGVyaW9kLFxyXG4gICAgICAgICAgICAgIHByaWNlOiB1c2VyRG9tYWluLnByaWNlLFxyXG4gICAgICAgICAgICAgIG9yZGVySWQ6IGFwaURvbWFpbi5vcmRlcklkIHx8IHVzZXJEb21haW4ub3JkZXJJZCxcclxuICAgICAgICAgICAgICBvcmRlclN0YXR1czogYXBpRG9tYWluLm9yZGVyU3RhdHVzIHx8IHVzZXJEb21haW4ub3JkZXJTdGF0dXMsXHJcblxyXG4gICAgICAgICAgICAgIC8vIFJlYWwgY29udGFjdCBkZXRhaWxzIGZyb20gQVBJXHJcbiAgICAgICAgICAgICAgY29udGFjdHM6IHtcclxuICAgICAgICAgICAgICAgIHJlZ2lzdHJhbnQ6IGFwaURvbWFpbi5jb250YWN0RGV0YWlscz8ucmVnaXN0cmFudCA/IHtcclxuICAgICAgICAgICAgICAgICAgbmFtZTogYXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLnJlZ2lzdHJhbnQubmFtZSxcclxuICAgICAgICAgICAgICAgICAgZW1haWw6IGFwaURvbWFpbi5jb250YWN0RGV0YWlscy5yZWdpc3RyYW50LmVtYWlsYWRkcixcclxuICAgICAgICAgICAgICAgICAgcGhvbmU6IGArJHthcGlEb21haW4uY29udGFjdERldGFpbHMucmVnaXN0cmFudC50ZWxub2NjfSAke2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5yZWdpc3RyYW50LnRlbG5vfWAsXHJcbiAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IGAke2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5yZWdpc3RyYW50LmFkZHJlc3MxfSwgJHthcGlEb21haW4uY29udGFjdERldGFpbHMucmVnaXN0cmFudC5jaXR5fSwgJHthcGlEb21haW4uY29udGFjdERldGFpbHMucmVnaXN0cmFudC5jb3VudHJ5fSAke2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5yZWdpc3RyYW50LnppcH1gLFxyXG4gICAgICAgICAgICAgICAgICBjb21wYW55OiBhcGlEb21haW4uY29udGFjdERldGFpbHMucmVnaXN0cmFudC5jb21wYW55LFxyXG4gICAgICAgICAgICAgICAgICBjb250YWN0SWQ6IGFwaURvbWFpbi5jb250YWN0RGV0YWlscy5yZWdpc3RyYW50LmNvbnRhY3RpZCxcclxuICAgICAgICAgICAgICAgIH0gOiB7XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICAgIGVtYWlsOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgICBwaG9uZTogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgICAgYWRkcmVzczogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBhZG1pbjogYXBpRG9tYWluLmNvbnRhY3REZXRhaWxzPy5hZG1pbiA/IHtcclxuICAgICAgICAgICAgICAgICAgbmFtZTogYXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLmFkbWluLm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgIGVtYWlsOiBhcGlEb21haW4uY29udGFjdERldGFpbHMuYWRtaW4uZW1haWxhZGRyLFxyXG4gICAgICAgICAgICAgICAgICBwaG9uZTogYCske2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5hZG1pbi50ZWxub2NjfSAke2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5hZG1pbi50ZWxub31gLFxyXG4gICAgICAgICAgICAgICAgICBhZGRyZXNzOiBgJHthcGlEb21haW4uY29udGFjdERldGFpbHMuYWRtaW4uYWRkcmVzczF9LCAke2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5hZG1pbi5jaXR5fSwgJHthcGlEb21haW4uY29udGFjdERldGFpbHMuYWRtaW4uY291bnRyeX0gJHthcGlEb21haW4uY29udGFjdERldGFpbHMuYWRtaW4uemlwfWAsXHJcbiAgICAgICAgICAgICAgICAgIGNvbXBhbnk6IGFwaURvbWFpbi5jb250YWN0RGV0YWlscy5hZG1pbi5jb21wYW55LFxyXG4gICAgICAgICAgICAgICAgICBjb250YWN0SWQ6IGFwaURvbWFpbi5jb250YWN0RGV0YWlscy5hZG1pbi5jb250YWN0aWQsXHJcbiAgICAgICAgICAgICAgICB9IDoge1xyXG4gICAgICAgICAgICAgICAgICBuYW1lOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgICBlbWFpbDogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgICAgcGhvbmU6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgdGVjaG5pY2FsOiBhcGlEb21haW4uY29udGFjdERldGFpbHM/LnRlY2ggPyB7XHJcbiAgICAgICAgICAgICAgICAgIG5hbWU6IGFwaURvbWFpbi5jb250YWN0RGV0YWlscy50ZWNoLm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgIGVtYWlsOiBhcGlEb21haW4uY29udGFjdERldGFpbHMudGVjaC5lbWFpbGFkZHIsXHJcbiAgICAgICAgICAgICAgICAgIHBob25lOiBgKyR7YXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLnRlY2gudGVsbm9jY30gJHthcGlEb21haW4uY29udGFjdERldGFpbHMudGVjaC50ZWxub31gLFxyXG4gICAgICAgICAgICAgICAgICBhZGRyZXNzOiBgJHthcGlEb21haW4uY29udGFjdERldGFpbHMudGVjaC5hZGRyZXNzMX0sICR7YXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLnRlY2guY2l0eX0sICR7YXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLnRlY2guY291bnRyeX0gJHthcGlEb21haW4uY29udGFjdERldGFpbHMudGVjaC56aXB9YCxcclxuICAgICAgICAgICAgICAgICAgY29tcGFueTogYXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLnRlY2guY29tcGFueSxcclxuICAgICAgICAgICAgICAgICAgY29udGFjdElkOiBhcGlEb21haW4uY29udGFjdERldGFpbHMudGVjaC5jb250YWN0aWQsXHJcbiAgICAgICAgICAgICAgICB9IDoge1xyXG4gICAgICAgICAgICAgICAgICBuYW1lOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgICBlbWFpbDogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgICAgcGhvbmU6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgYmlsbGluZzogYXBpRG9tYWluLmNvbnRhY3REZXRhaWxzPy5iaWxsaW5nID8ge1xyXG4gICAgICAgICAgICAgICAgICBuYW1lOiBhcGlEb21haW4uY29udGFjdERldGFpbHMuYmlsbGluZy5uYW1lLFxyXG4gICAgICAgICAgICAgICAgICBlbWFpbDogYXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLmJpbGxpbmcuZW1haWxhZGRyLFxyXG4gICAgICAgICAgICAgICAgICBwaG9uZTogYCske2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5iaWxsaW5nLnRlbG5vY2N9ICR7YXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLmJpbGxpbmcudGVsbm99YCxcclxuICAgICAgICAgICAgICAgICAgYWRkcmVzczogYCR7YXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLmJpbGxpbmcuYWRkcmVzczF9LCAke2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5iaWxsaW5nLmNpdHl9LCAke2FwaURvbWFpbi5jb250YWN0RGV0YWlscy5iaWxsaW5nLmNvdW50cnl9ICR7YXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLmJpbGxpbmcuemlwfWAsXHJcbiAgICAgICAgICAgICAgICAgIGNvbXBhbnk6IGFwaURvbWFpbi5jb250YWN0RGV0YWlscy5iaWxsaW5nLmNvbXBhbnksXHJcbiAgICAgICAgICAgICAgICAgIGNvbnRhY3RJZDogYXBpRG9tYWluLmNvbnRhY3REZXRhaWxzLmJpbGxpbmcuY29udGFjdGlkLFxyXG4gICAgICAgICAgICAgICAgfSA6IHtcclxuICAgICAgICAgICAgICAgICAgbmFtZTogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgICAgZW1haWw6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICAgIHBob25lOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgICBhZGRyZXNzOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICB9LFxyXG5cclxuICAgICAgICAgICAgICAvLyBDb250YWN0IElEcyBmb3IgQVBJIG9wZXJhdGlvbnNcclxuICAgICAgICAgICAgICBjb250YWN0SWRzOiBhcGlEb21haW4uY29udGFjdHMsXHJcblxyXG4gICAgICAgICAgICAgIC8vIEFkZGl0aW9uYWwgcmVhbCBkYXRhIGZyb20gQVBJXHJcbiAgICAgICAgICAgICAgcHJvZHVjdENhdGVnb3J5OiBhcGlEb21haW4ucHJvZHVjdENhdGVnb3J5LFxyXG4gICAgICAgICAgICAgIHByb2R1Y3RLZXk6IGFwaURvbWFpbi5wcm9kdWN0S2V5LFxyXG4gICAgICAgICAgICAgIGN1c3RvbWVySWQ6IGFwaURvbWFpbi5jdXN0b21lcklkLFxyXG4gICAgICAgICAgICAgIGdkcHI6IGFwaURvbWFpbi5nZHByLFxyXG4gICAgICAgICAgICAgIGxvY2tzOiBhcGlEb21haW4ubG9ja3MsXHJcbiAgICAgICAgICAgICAgcmFhVmVyaWZpY2F0aW9uOiBhcGlEb21haW4ucmFhVmVyaWZpY2F0aW9uLFxyXG4gICAgICAgICAgICAgIGRuc3NlYzogYXBpRG9tYWluLmRuc3NlYyxcclxuXHJcbiAgICAgICAgICAgICAgLy8gUmF3IEFQSSByZXNwb25zZSBmb3IgZGVidWdnaW5nXHJcbiAgICAgICAgICAgICAgYXBpRGV0YWlsczogYXBpRG9tYWluLFxyXG5cclxuICAgICAgICAgICAgICAvLyBEZWZhdWx0IEROUyByZWNvcmRzIChwbGFjZWhvbGRlciAtIHdvdWxkIG5lZWQgc2VwYXJhdGUgQVBJIGNhbGwpXHJcbiAgICAgICAgICAgICAgZG5zUmVjb3JkczogW1xyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICBpZDogXCJyZWMxXCIsXHJcbiAgICAgICAgICAgICAgICAgIHR5cGU6IFwiQVwiLFxyXG4gICAgICAgICAgICAgICAgICBuYW1lOiBcIkBcIixcclxuICAgICAgICAgICAgICAgICAgY29udGVudDogXCJETlMgcmVjb3JkcyBhdmFpbGFibGUgdmlhIHNlcGFyYXRlIEFQSVwiLFxyXG4gICAgICAgICAgICAgICAgICB0dGw6IDM2MDAsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICBzZXREb21haW4oY29tYmluZWREb21haW4pO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gZG9tYWluIGRhdGEgcmVjZWl2ZWQgZnJvbSBBUElcIik7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoYXBpRXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUud2FybihcIkNvdWxkIG5vdCBmZXRjaCBkb21haW4gZGV0YWlscyBmcm9tIEFQSTpcIiwgYXBpRXJyb3IpO1xyXG5cclxuICAgICAgICAgIC8vIEZhbGxiYWNrIHRvIHVzZXIgZG9tYWluIGRhdGEgb25seVxyXG4gICAgICAgICAgY29uc3QgZmFsbGJhY2tEb21haW4gPSB7XHJcbiAgICAgICAgICAgIGlkOiB1c2VyRG9tYWluLmlkLFxyXG4gICAgICAgICAgICBuYW1lOiB1c2VyRG9tYWluLm5hbWUsXHJcbiAgICAgICAgICAgIHN0YXR1czogdXNlckRvbWFpbi5zdGF0dXMsXHJcbiAgICAgICAgICAgIHJlZ2lzdHJhdGlvbkRhdGU6IHVzZXJEb21haW4ucmVnaXN0cmF0aW9uRGF0ZSxcclxuICAgICAgICAgICAgZXhwaXJ5RGF0ZTogdXNlckRvbWFpbi5leHBpcnlEYXRlLFxyXG4gICAgICAgICAgICBhdXRvUmVuZXc6IHVzZXJEb21haW4uYXV0b1JlbmV3LFxyXG4gICAgICAgICAgICByZWdpc3RyYXI6IHVzZXJEb21haW4ucmVnaXN0cmFyIHx8IFwiWlRlY2ggRG9tYWluc1wiLFxyXG4gICAgICAgICAgICBuYW1lc2VydmVyczogdXNlckRvbWFpbi5uYW1lc2VydmVycyB8fCBbXHJcbiAgICAgICAgICAgICAgXCJuczEuenRlY2hcIixcclxuICAgICAgICAgICAgICBcIm5zMi56dGVjaFwiLFxyXG4gICAgICAgICAgICAgIFwibnMzLnp0ZWNoXCIsXHJcbiAgICAgICAgICAgICAgXCJuczQuenRlY2hcIixcclxuICAgICAgICAgICAgXSxcclxuICAgICAgICAgICAgcHJpdmFjeVByb3RlY3Rpb246IHVzZXJEb21haW4ucHJpdmFjeVByb3RlY3Rpb24sXHJcbiAgICAgICAgICAgIHBlcmlvZDogdXNlckRvbWFpbi5wZXJpb2QsXHJcbiAgICAgICAgICAgIHByaWNlOiB1c2VyRG9tYWluLnByaWNlLFxyXG4gICAgICAgICAgICBvcmRlcklkOiB1c2VyRG9tYWluLm9yZGVySWQsXHJcbiAgICAgICAgICAgIG9yZGVyU3RhdHVzOiB1c2VyRG9tYWluLm9yZGVyU3RhdHVzLFxyXG4gICAgICAgICAgICBjb250YWN0czoge1xyXG4gICAgICAgICAgICAgIHJlZ2lzdHJhbnQ6IHtcclxuICAgICAgICAgICAgICAgIG5hbWU6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICBlbWFpbDogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgIHBob25lOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgYWRkcmVzczogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIGFkbWluOiB7XHJcbiAgICAgICAgICAgICAgICBuYW1lOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgZW1haWw6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICBwaG9uZTogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgIGFkZHJlc3M6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICB0ZWNobmljYWw6IHtcclxuICAgICAgICAgICAgICAgIG5hbWU6IFwiQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlXCIsXHJcbiAgICAgICAgICAgICAgICBlbWFpbDogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgIHBob25lOiBcIkNvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZVwiLFxyXG4gICAgICAgICAgICAgICAgYWRkcmVzczogXCJDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBkbnNSZWNvcmRzOiBbXHJcbiAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgaWQ6IFwicmVjMVwiLFxyXG4gICAgICAgICAgICAgICAgdHlwZTogXCJBXCIsXHJcbiAgICAgICAgICAgICAgICBuYW1lOiBcIkBcIixcclxuICAgICAgICAgICAgICAgIGNvbnRlbnQ6IFwiRE5TIGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGVcIixcclxuICAgICAgICAgICAgICAgIHR0bDogMzYwMCxcclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBdLFxyXG4gICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICBzZXREb21haW4oZmFsbGJhY2tEb21haW4pO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGdldHRpbmcgZG9tYWluIGRldGFpbHNcIiwgZXJyb3IpO1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gICAgZ2V0RG9tYWluRGV0YWlscygpO1xyXG4gIH0sIFtpZF0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVBdXRvUmVuZXdUb2dnbGUgPSBhc3luYyAodmFsdWUpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFRoaXMgd291bGQgYmUgcmVwbGFjZWQgd2l0aCBhY3R1YWwgQVBJIGNhbGwgd2hlbiBpbXBsZW1lbnRlZFxyXG4gICAgICAvLyBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLnRvZ2dsZUF1dG9SZW5ld2FsKGlkLCB2YWx1ZSk7XHJcbiAgICAgIHNldERvbWFpbih7IC4uLmRvbWFpbiwgYXV0b1JlbmV3OiB2YWx1ZSB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB0b2dnbGluZyBhdXRvIHJlbmV3YWxcIiwgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVByaXZhY3lUb2dnbGUgPSBhc3luYyAodmFsdWUpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGDwn5SnIFtQUklWQUNZXSBUb2dnbGluZyBwcml2YWN5IHByb3RlY3Rpb24gZm9yIGRvbWFpbiAke2RvbWFpbi5uYW1lfTpgLCB7XHJcbiAgICAgICAgZnJvbTogZG9tYWluLnByaXZhY3lQcm90ZWN0aW9uLFxyXG4gICAgICAgIHRvOiB2YWx1ZSxcclxuICAgICAgICBvcmRlcklkOiBkb21haW4ub3JkZXJJZFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmICghZG9tYWluLm9yZGVySWQpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwi8J+UpyBbUFJJVkFDWV0g4p2MIE5vIG9yZGVyIElEIGF2YWlsYWJsZSBmb3IgZG9tYWluXCIpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQ2FsbCB0aGUgbW9kaWZ5IHByaXZhY3kgcHJvdGVjdGlvbiBBUElcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLm1vZGlmeVByaXZhY3lQcm90ZWN0aW9uKHtcclxuICAgICAgICBvcmRlcklkOiBkb21haW4ub3JkZXJJZCxcclxuICAgICAgICBwcm90ZWN0UHJpdmFjeTogdmFsdWUsXHJcbiAgICAgICAgcmVhc29uOiBgVXNlciAke3ZhbHVlID8gJ2VuYWJsZWQnIDogJ2Rpc2FibGVkJ30gcHJpdmFjeSBwcm90ZWN0aW9uIHZpYSBkb21haW4gbWFuYWdlbWVudCBwYW5lbGBcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIvCflKcgW1BSSVZBQ1ldIOKchSBQcml2YWN5IHByb3RlY3Rpb24gdXBkYXRlZCBzdWNjZXNzZnVsbHk6XCIsIHJlc3BvbnNlLmRhdGEpO1xyXG5cclxuICAgICAgLy8gVXBkYXRlIGxvY2FsIHN0YXRlXHJcbiAgICAgIHNldERvbWFpbih7IC4uLmRvbWFpbiwgcHJpdmFjeVByb3RlY3Rpb246IHZhbHVlIH0pO1xyXG5cclxuICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2VcclxuICAgICAgdG9hc3Quc3VjY2VzcyhgUHJpdmFjeSBwcm90ZWN0aW9uICR7dmFsdWUgPyAnZW5hYmxlZCcgOiAnZGlzYWJsZWQnfSBzdWNjZXNzZnVsbHlgKTtcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwi8J+UpyBbUFJJVkFDWV0g4p2MIEVycm9yIHRvZ2dsaW5nIHByaXZhY3kgcHJvdGVjdGlvbjpcIiwgZXJyb3IpO1xyXG4gICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byB1cGRhdGUgcHJpdmFjeSBwcm90ZWN0aW9uLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBpZiAobG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1zY3JlZW5cIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2UgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIGJnLWJsdWUtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgIDxHbG9iZSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtYmx1ZS02MDBcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgIHt0KFwibG9hZGluZ1wiKX0uLi5cclxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKCFkb21haW4pIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuIHAtOFwiPlxyXG4gICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNFwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS04MDAgZm9udC1ib2xkIG1iLTJcIj5cclxuICAgICAgICAgIHt0KFwiZG9tYWluX25vdF9mb3VuZFwiLCB7IGRlZmF1bHRWYWx1ZTogXCJEb21haW4gTm90IEZvdW5kXCIgfSl9XHJcbiAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgIDxCdXR0b25cclxuICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTQgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goXCIvY2xpZW50L2RvbWFpbnNcIil9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgIHtkdChcImJhY2tfdG9fZG9tYWluc1wiKX1cclxuICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwicC04IGJnLWdyYXktNTAgbWluLWgtc2NyZWVuXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG9cIj5cclxuICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICB2YXJpYW50PVwidGV4dFwiXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJtYi02IHRleHQtYmx1ZS02MDAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goXCIvY2xpZW50L2RvbWFpbnNcIil9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgIHtkdChcImJhY2tfdG9fZG9tYWluc1wiKX1cclxuICAgICAgICA8L0J1dHRvbj5cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtZDppdGVtcy1jZW50ZXIgbWItOCBnYXAtNFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBiZy1ibHVlLTEwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1yLTRcIj5cclxuICAgICAgICAgICAgICA8R2xvYmUgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWJsdWUtNjAwXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJoMVwiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge2RvbWFpbi5uYW1lfVxyXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTFcIj5cclxuICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGNhcGl0YWxpemUgbXItMiAke2RvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgPT09IFwiYWN0aXZlXCJcclxuICAgICAgICAgICAgICAgICAgICA/IFwiYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwXCJcclxuICAgICAgICAgICAgICAgICAgICA6IGRvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgPT09IFwicGVuZGluZ1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgOiBkb21haW4uc3RhdHVzPy50b0xvd2VyQ2FzZSgpID09PSBcImV4cGlyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctcmVkLTEwMCB0ZXh0LXJlZC04MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGRvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgPT09IFwiZmFpbGVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctcmVkLTEwMCB0ZXh0LXJlZC04MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwXCJcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge2R0KGRvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgfHwgXCJ1bmtub3duXCIpfVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIHtkdChcInJlZ2lzdHJhclwiKX06IHtkb21haW4ucmVnaXN0cmFyfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItYmx1ZS02MDAgdGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTUwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cub3BlbihgaHR0cDovLyR7ZG9tYWluLm5hbWV9YCwgXCJfYmxhbmtcIil9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7dChcInZpc2l0X3dlYnNpdGVcIiwgeyBkZWZhdWx0VmFsdWU6IFwiVmlzaXQgV2Vic2l0ZVwiIH0pfVxyXG4gICAgICAgICAgICAgIDxFeHRlcm5hbExpbmsgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvY2xpZW50L2RvbWFpbnMvJHtpZH0vcmVuZXdgKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHtkdChcInJlbmV3X2RvbWFpblwiKX1cclxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8VGFicyB2YWx1ZT17YWN0aXZlVGFifSBjbGFzc05hbWU9XCJtYi04XCI+XHJcbiAgICAgICAgICA8VGFic0hlYWRlclxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCByb3VuZGVkLWxnIHAtMVwiXHJcbiAgICAgICAgICAgIGluZGljYXRvclByb3BzPXt7XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lOiBcImJnLXdoaXRlIHNoYWRvdy1tZCByb3VuZGVkLW1kXCIsXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxUYWJcclxuICAgICAgICAgICAgICB2YWx1ZT1cIm92ZXJ2aWV3XCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoXCJvdmVydmlld1wiKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2FjdGl2ZVRhYiA9PT0gXCJvdmVydmlld1wiID8gXCJ0ZXh0LWJsdWUtNjAwXCIgOiBcIlwifVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge3QoXCJvdmVydmlld1wiLCB7IGRlZmF1bHRWYWx1ZTogXCJPdmVydmlld1wiIH0pfVxyXG4gICAgICAgICAgICA8L1RhYj5cclxuICAgICAgICAgICAgPFRhYlxyXG4gICAgICAgICAgICAgIHZhbHVlPVwiZG5zXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoXCJkbnNcIil9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXthY3RpdmVUYWIgPT09IFwiZG5zXCIgPyBcInRleHQtYmx1ZS02MDBcIiA6IFwiXCJ9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7ZHQoXCJkbnNfc2V0dGluZ3NcIil9XHJcbiAgICAgICAgICAgIDwvVGFiPlxyXG4gICAgICAgICAgICA8VGFiXHJcbiAgICAgICAgICAgICAgdmFsdWU9XCJjb250YWN0c1wiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKFwiY29udGFjdHNcIil9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXthY3RpdmVUYWIgPT09IFwiY29udGFjdHNcIiA/IFwidGV4dC1ibHVlLTYwMFwiIDogXCJcIn1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHtkdChcImRvbWFpbl9jb250YWN0c1wiKX1cclxuICAgICAgICAgICAgPC9UYWI+XHJcbiAgICAgICAgICAgIDxUYWJcclxuICAgICAgICAgICAgICB2YWx1ZT1cInByaXZhY3lcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYihcInByaXZhY3lcIil9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXthY3RpdmVUYWIgPT09IFwicHJpdmFjeVwiID8gXCJ0ZXh0LWJsdWUtNjAwXCIgOiBcIlwifVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge3QoXCJwcml2YWN5XCIsIHsgZGVmYXVsdFZhbHVlOiBcIlByaXZhY3lcIiB9KX1cclxuICAgICAgICAgICAgPC9UYWI+XHJcbiAgICAgICAgICA8L1RhYnNIZWFkZXI+XHJcbiAgICAgICAgICA8VGFic0JvZHk+XHJcbiAgICAgICAgICAgIDxUYWJQYW5lbCB2YWx1ZT1cIm92ZXJ2aWV3XCIgY2xhc3NOYW1lPVwicC0wIG10LTZcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICAgICAgPENhcmRCb2R5IGNsYXNzTmFtZT1cInAtNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7ZHQoXCJkb21haW5fZGV0YWlsc1wiKX1cclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtkdChcImRvbWFpbl9uYW1lXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2RvbWFpbi5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIE9yZGVyIElEXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICN7ZG9tYWluLm9yZGVySWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2R0KFwic3RhdHVzXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gY2FwaXRhbGl6ZSAke2RvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgPT09IFwiYWN0aXZlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBkb21haW4uc3RhdHVzPy50b0xvd2VyQ2FzZSgpID09PSBcInBlbmRpbmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGRvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgPT09IFwiZXhwaXJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXJlZC0xMDAgdGV4dC1yZWQtODAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGRvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgPT09IFwiZmFpbGVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1yZWQtMTAwIHRleHQtcmVkLTgwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtkdChkb21haW4uc3RhdHVzPy50b0xvd2VyQ2FzZSgpIHx8IFwidW5rbm93blwiKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZHQoXCJyZWdpc3RyYXRpb25fZGF0ZVwiKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERhdGUoZG9tYWluLnJlZ2lzdHJhdGlvbkRhdGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtkdChcImV4cGlyeV9kYXRlXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShkb21haW4uZXhwaXJ5RGF0ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2R0KFwiYXV0b19yZW5ld1wiKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8U3dpdGNoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17ZG9tYWluLmF1dG9SZW5ld31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVBdXRvUmVuZXdUb2dnbGUoZS50YXJnZXQuY2hlY2tlZClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiBTZWN1cml0eSAmIFByb3RlY3Rpb24gKi99XHJcbiAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkQm9keSBjbGFzc05hbWU9XCJwLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgU2VjdXJpdHkgJiBQcm90ZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtkdChcIndob2lzX3ByaXZhY3lcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWVuZCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBDaGVjayBpZiBwcml2YWN5IHByb3RlY3Rpb24gc2VydmljZSBpcyBwdXJjaGFzZWQgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2RvbWFpbi5wcml2YWN5UHJvdGVjdGlvbkRldGFpbHM/LnB1cmNoYXNlZCAhPT0gZmFsc2UgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBQcml2YWN5IHByb3RlY3Rpb24gaXMgcHVyY2hhc2VkIC0gc2hvdyB0b2dnbGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFN3aXRjaFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2RvbWFpbi5wcml2YWN5UHJvdGVjdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVQcml2YWN5VG9nZ2xlKGUudGFyZ2V0LmNoZWNrZWQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPVwiYmx1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQgJHtkb21haW4ucHJpdmFjeVByb3RlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWdyYXktMTAwIHRleHQtZ3JheS02MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZG9tYWluLnByaXZhY3lQcm90ZWN0aW9uID8gXCJFbmFibGVkXCIgOiBcIkRpc2FibGVkXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBQcml2YWN5IHByb3RlY3Rpb24gbm90IHB1cmNoYXNlZCAtIHNob3cgcHVyY2hhc2Ugb3B0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtZW5kIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgcHgtMiBweS0xIGJnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwIHJvdW5kZWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBOb3QgUHVyY2hhc2VkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgcHgtMyBweS0xXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVQdXJjaGFzZVByaXZhY3koKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFB1cmNoYXNlIFByaXZhY3kgUHJvdGVjdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAge2RvbWFpbi5vcmRlclN0YXR1cyAmJiBBcnJheS5pc0FycmF5KGRvbWFpbi5vcmRlclN0YXR1cykgJiYgZG9tYWluLm9yZGVyU3RhdHVzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBEb21haW4gTG9ja3NcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZG9tYWluLm9yZGVyU3RhdHVzLm1hcCgobG9jaywgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC14cyBweC0yIHB5LTEgYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAgcm91bmRlZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsb2NrfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIHtkb21haW4uZG9tYWluU3RhdHVzICYmIEFycmF5LmlzQXJyYXkoZG9tYWluLmRvbWFpblN0YXR1cykgJiYgZG9tYWluLmRvbWFpblN0YXR1cy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgUmVnaXN0cnkgU3RhdHVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2RvbWFpbi5kb21haW5TdGF0dXMubWFwKChzdGF0dXMsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQteHMgcHgtMiBweS0xIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgcm91bmRlZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGF0dXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAge2RvbWFpbi5yYWFWZXJpZmljYXRpb24/LnN0YXR1cyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBSQUEgVmVyaWZpY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQgJHtkb21haW4ucmFhVmVyaWZpY2F0aW9uLnN0YXR1cyA9PT0gXCJWZXJpZmllZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogZG9tYWluLnJhYVZlcmlmaWNhdGlvbi5zdGF0dXMgPT09IFwiUGVuZGluZ1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1yZWQtMTAwIHRleHQtcmVkLTgwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZG9tYWluLnJhYVZlcmlmaWNhdGlvbi5zdGF0dXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAge2RvbWFpbi5nZHByPy5lbmFibGVkICE9PSB1bmRlZmluZWQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgR0RQUiBQcm90ZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQgJHtkb21haW4uZ2Rwci5lbmFibGVkID09PSBcInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctZ3JheS0xMDAgdGV4dC1ncmF5LTYwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZG9tYWluLmdkcHIuZW5hYmxlZCA9PT0gXCJ0cnVlXCIgPyBcIkVuYWJsZWRcIiA6IFwiRGlzYWJsZWRcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICB7LyogUmVnaXN0cmF0aW9uIEVycm9yIERpc3BsYXkgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICB7KGRvbWFpbi5zdGF0dXM/LnRvTG93ZXJDYXNlKCkgPT09IFwiZmFpbGVkXCIgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZG9tYWluLm9yZGVyU3RhdHVzID09PSBcIkZBSUxFRFwiIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRvbWFpbi5yZWdpc3RyYXRpb25FcnJvcikgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZWdpc3RyYXRpb24gU3RhdHVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBweC0yIHB5LTEgYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAgcm91bmRlZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlZ2lzdHJhdGlvbiBGYWlsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZG9tYWluLnJlZ2lzdHJhdGlvbkVycm9yICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHB4LTIgcHktMSBiZy1yZWQtNTAgdGV4dC1yZWQtNzAwIHJvdW5kZWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkb21haW4ucmVnaXN0cmF0aW9uRXJyb3J9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIE5hbWVzZXJ2ZXJzICovfVxyXG4gICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZEJvZHkgY2xhc3NOYW1lPVwicC02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtkdChcIm5hbWVzZXJ2ZXJzXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2RvbWFpbi5uYW1lc2VydmVycyAmJiBBcnJheS5pc0FycmF5KGRvbWFpbi5uYW1lc2VydmVycykgJiYgZG9tYWluLm5hbWVzZXJ2ZXJzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRvbWFpbi5uYW1lc2VydmVycy5tYXAoKG5zLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBOUyB7aW5kZXggKyAxfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57bnN9PC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApKVxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZG9tYWluLnN0YXR1cz8udG9Mb3dlckNhc2UoKSA9PT0gXCJmYWlsZWRcIiB8fCBkb21haW4ub3JkZXJTdGF0dXMgPT09IFwiRkFJTEVEXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIk5hbWVzZXJ2ZXJzIG5vdCBhdmFpbGFibGUgLSBSZWdpc3RyYXRpb24gZmFpbGVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIk5vIG5hbWVzZXJ2ZXJzIGNvbmZpZ3VyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXItYmx1ZS02MDAgdGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoXCJkbnNcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZHQoXCJ1cGRhdGVfbmFtZXNlcnZlcnNcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcblxyXG4gICAgICAgICAgICA8VGFiUGFuZWwgdmFsdWU9XCJkbnNcIiBjbGFzc05hbWU9XCJwLTAgbXQtNlwiPlxyXG4gICAgICAgICAgICAgIHsvKiBOYW1lc2VydmVyIE1hbmFnZW1lbnQgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgICAgICA8TmFtZXNlcnZlck1hbmFnZXJcclxuICAgICAgICAgICAgICAgICAgZG9tYWluPXtkb21haW59XHJcbiAgICAgICAgICAgICAgICAgIG9uVXBkYXRlPXsodXBkYXRlZERvbWFpbikgPT4gc2V0RG9tYWluKHVwZGF0ZWREb21haW4pfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgey8qIEROUyBSZWNvcmRzIFRhYiBDb250ZW50ICovfVxyXG4gICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkQm9keSBjbGFzc05hbWU9XCJwLTZcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2R0KFwibWFuYWdlX2Ruc19yZWNvcmRzXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaChgL2NsaWVudC9kb21haW5zLyR7aWR9L2Rucy9hZGRgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHt0KFwiYWRkX3JlY29yZFwiLCB7IGRlZmF1bHRWYWx1ZTogXCJBZGQgUmVjb3JkXCIgfSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDx0aGVhZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJnLWdyYXktNTAgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dChcInR5cGVcIiwgeyBkZWZhdWx0VmFsdWU6IFwiVHlwZVwiIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dChcIm5hbWVcIiwgeyBkZWZhdWx0VmFsdWU6IFwiTmFtZVwiIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dChcImNvbnRlbnRcIiwgeyBkZWZhdWx0VmFsdWU6IFwiQ29udGVudFwiIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dChcInR0bFwiLCB7IGRlZmF1bHRWYWx1ZTogXCJUVExcIiB9KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0KFwiYWN0aW9uc1wiLCB7IGRlZmF1bHRWYWx1ZTogXCJBY3Rpb25zXCIgfSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtkb21haW4uZG5zUmVjb3Jkcy5tYXAoKHJlY29yZCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e3JlY29yZC5pZH0gY2xhc3NOYW1lPVwiaG92ZXI6YmctZ3JheS01MFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlY29yZC50eXBlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZWNvcmQubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS0zIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVjb3JkLmNvbnRlbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlY29yZC50dGx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2goXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGAvY2xpZW50L2RvbWFpbnMvJHtpZH0vZG5zLyR7cmVjb3JkLmlkfWBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dChcImVkaXRcIiwgeyBkZWZhdWx0VmFsdWU6IFwiRWRpdFwiIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3Rib2R5PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvdGFibGU+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcblxyXG4gICAgICAgICAgICA8VGFiUGFuZWwgdmFsdWU9XCJjb250YWN0c1wiIGNsYXNzTmFtZT1cInAtMCBtdC02XCI+XHJcbiAgICAgICAgICAgICAgey8qIERvbWFpbiBDb250YWN0cyBUYWIgQ29udGVudCAqL31cclxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZEJvZHkgY2xhc3NOYW1lPVwicC02XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2R0KFwiZG9tYWluX2NvbnRhY3RzXCIpfVxyXG4gICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3QoXCJyZWdpc3RyYW50XCIsIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0VmFsdWU6IFwiUmVnaXN0cmFudCBDb250YWN0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtNCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNDb250YWN0RGF0YShkb21haW4uY29udGFjdHM/LnJlZ2lzdHJhbnQpID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMucmVnaXN0cmFudCwgJ25hbWUnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMucmVnaXN0cmFudCwgJ2NvbXBhbnknLCBudWxsKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMucmVnaXN0cmFudCwgJ2NvbXBhbnknKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfk6cge2dldENvbnRhY3RJbmZvKGRvbWFpbi5jb250YWN0cy5yZWdpc3RyYW50LCAnZW1haWwnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDwn5OeIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMucmVnaXN0cmFudCwgJ3Bob25lJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TjSB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLnJlZ2lzdHJhbnQsICdhZGRyZXNzJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLnJlZ2lzdHJhbnQsICdjb250YWN0SWQnLCBudWxsKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgSUQ6IHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMucmVnaXN0cmFudCwgJ2NvbnRhY3RJZCcpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBpdGFsaWNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZSBmcm9tIHJlc2VsbGVyIEFQSVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3QoXCJhZG1pblwiLCB7IGRlZmF1bHRWYWx1ZTogXCJBZG1pbmlzdHJhdGl2ZSBDb250YWN0XCIgfSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2hhc0NvbnRhY3REYXRhKGRvbWFpbi5jb250YWN0cz8uYWRtaW4pID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMuYWRtaW4sICduYW1lJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmFkbWluLCAnY29tcGFueScsIG51bGwpICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENvbnRhY3RJbmZvKGRvbWFpbi5jb250YWN0cy5hZG1pbiwgJ2NvbXBhbnknKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfk6cge2dldENvbnRhY3RJbmZvKGRvbWFpbi5jb250YWN0cy5hZG1pbiwgJ2VtYWlsJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TniB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmFkbWluLCAncGhvbmUnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDwn5ONIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMuYWRtaW4sICdhZGRyZXNzJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmFkbWluLCAnY29udGFjdElkJywgbnVsbCkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIElEOiB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmFkbWluLCAnY29udGFjdElkJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGl0YWxpY1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ29udGFjdCBpbmZvcm1hdGlvbiBub3QgYXZhaWxhYmxlIGZyb20gcmVzZWxsZXIgQVBJXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7dChcInRlY2huaWNhbFwiLCB7IGRlZmF1bHRWYWx1ZTogXCJUZWNobmljYWwgQ29udGFjdFwiIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtNCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNDb250YWN0RGF0YShkb21haW4uY29udGFjdHM/LnRlY2huaWNhbCkgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENvbnRhY3RJbmZvKGRvbWFpbi5jb250YWN0cy50ZWNobmljYWwsICduYW1lJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLnRlY2huaWNhbCwgJ2NvbXBhbnknLCBudWxsKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMudGVjaG5pY2FsLCAnY29tcGFueScpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TpyB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLnRlY2huaWNhbCwgJ2VtYWlsJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TniB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLnRlY2huaWNhbCwgJ3Bob25lJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TjSB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLnRlY2huaWNhbCwgJ2FkZHJlc3MnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMudGVjaG5pY2FsLCAnY29udGFjdElkJywgbnVsbCkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIElEOiB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLnRlY2huaWNhbCwgJ2NvbnRhY3RJZCcpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBpdGFsaWNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbnRhY3QgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZSBmcm9tIHJlc2VsbGVyIEFQSVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogQWRkIEJpbGxpbmcgQ29udGFjdCAqL31cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt0KFwiYmlsbGluZ1wiLCB7IGRlZmF1bHRWYWx1ZTogXCJCaWxsaW5nIENvbnRhY3RcIiB9KX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aGFzQ29udGFjdERhdGEoZG9tYWluLmNvbnRhY3RzPy5iaWxsaW5nKSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmJpbGxpbmcsICduYW1lJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmJpbGxpbmcsICdjb21wYW55JywgbnVsbCkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDAgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmJpbGxpbmcsICdjb21wYW55Jyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDwn5OnIHtnZXRDb250YWN0SW5mbyhkb21haW4uY29udGFjdHMuYmlsbGluZywgJ2VtYWlsJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TniB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmJpbGxpbmcsICdwaG9uZScpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfk40ge2dldENvbnRhY3RJbmZvKGRvbWFpbi5jb250YWN0cy5iaWxsaW5nLCAnYWRkcmVzcycpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldENvbnRhY3RJbmZvKGRvbWFpbi5jb250YWN0cy5iaWxsaW5nLCAnY29udGFjdElkJywgbnVsbCkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIElEOiB7Z2V0Q29udGFjdEluZm8oZG9tYWluLmNvbnRhY3RzLmJpbGxpbmcsICdjb250YWN0SWQnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgaXRhbGljXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDb250YWN0IGluZm9ybWF0aW9uIG5vdCBhdmFpbGFibGUgZnJvbSByZXNlbGxlciBBUElcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaChgL2NsaWVudC9kb21haW5zLyR7aWR9L2NvbnRhY3RzYClcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7ZHQoXCJ1cGRhdGVfY29udGFjdHNcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcblxyXG4gICAgICAgICAgICA8VGFiUGFuZWwgdmFsdWU9XCJwcml2YWN5XCIgY2xhc3NOYW1lPVwicC0wIG10LTZcIj5cclxuICAgICAgICAgICAgICB7LyogUHJpdmFjeSBQcm90ZWN0aW9uIFRhYiBDb250ZW50ICovfVxyXG4gICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkQm9keSBjbGFzc05hbWU9XCJwLTZcIj5cclxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICB7dChcInByaXZhY3lcIiwgeyBkZWZhdWx0VmFsdWU6IFwiUHJpdmFjeSBQcm90ZWN0aW9uXCIgfSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTE2IHctMTYgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge3QoXCJwcml2YWN5X2NvbnRlbnRfY29taW5nX3Nvb25cIiwge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0VmFsdWU6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXCJQcml2YWN5IHByb3RlY3Rpb24gc2V0dGluZ3Mgd2lsbCBiZSBhdmFpbGFibGUgc29vbi5cIixcclxuICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHt0KFwicHJpdmFjeV9kZXNjcmlwdGlvblwiLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHRWYWx1ZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcIk1hbmFnZSB5b3VyIGRvbWFpbiBwcml2YWN5IHByb3RlY3Rpb24gYW5kIFdIT0lTIGluZm9ybWF0aW9uIHZpc2liaWxpdHkuXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcbiAgICAgICAgICA8L1RhYnNCb2R5PlxyXG4gICAgICAgIDwvVGFicz5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVRyYW5zbGF0aW9ucyIsInVzZVJvdXRlciIsInRvYXN0IiwiVHlwb2dyYXBoeSIsIkNhcmQiLCJDYXJkQm9keSIsIkJ1dHRvbiIsIlRhYnMiLCJUYWJzSGVhZGVyIiwiVGFic0JvZHkiLCJUYWIiLCJUYWJQYW5lbCIsIlN3aXRjaCIsIkdsb2JlIiwiQXJyb3dMZWZ0IiwiU2VydmVyIiwiU2hpZWxkIiwiTG9jayIsIk1haWwiLCJFeHRlcm5hbExpbmsiLCJSZWZyZXNoQ3ciLCJkb21haW5NbmdTZXJ2aWNlIiwiTmFtZXNlcnZlck1hbmFnZXIiLCJQcml2YWN5UHJvdGVjdGlvbk1hbmFnZXIiLCJEb21haW5EZXRhaWxQYWdlIiwicGFyYW1zIiwiZG9tYWluIiwiaWQiLCJ0IiwiZHQiLCJzZXREb21haW4iLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInJvdXRlciIsImZvcm1hdERhdGUiLCJ1bml4VGltZXN0YW1wIiwiZGF0ZSIsIkRhdGUiLCJwYXJzZUludCIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInllYXIiLCJtb250aCIsImRheSIsImhvdXIiLCJtaW51dGUiLCJlcnJvciIsImZvcm1hdFN0YXR1cyIsInN0YXR1cyIsInRvTG93ZXJDYXNlIiwicmVwbGFjZSIsImdldENvbnRhY3RJbmZvIiwiY29udGFjdCIsImZpZWxkIiwiZmFsbGJhY2siLCJoYXNDb250YWN0RGF0YSIsIm5hbWUiLCJlbWFpbCIsImdldERvbWFpbkRldGFpbHMiLCJkb21haW5zUmVzIiwiZ2V0VXNlckRvbWFpbnMiLCJ1c2VyRG9tYWlucyIsImRhdGEiLCJkb21haW5zIiwidXNlckRvbWFpbiIsImZpbmQiLCJkIiwiY29uc29sZSIsImxvZyIsImRldGFpbHNSZXMiLCJhcGlEb21haW4iLCJnZXREb21haW5EZXRhaWxzQnlOYW1lIiwiYXBpUHJpdmFjeVByb3RlY3Rpb24iLCJwcml2YWN5UHJvdGVjdGlvbiIsInVzZXJQcml2YWN5UHJvdGVjdGlvbiIsImFwaVByaXZhY3lFbmFibGVkIiwiZW5hYmxlZCIsIndpbGxVc2VBcGlEYXRhIiwidW5kZWZpbmVkIiwiY29tYmluZWREb21haW4iLCJkb21haW5OYW1lIiwicmVnaXN0cmF0aW9uRGF0ZSIsImV4cGlyeURhdGUiLCJhdXRvUmVuZXciLCJyZWdpc3RyYXIiLCJuYW1lc2VydmVycyIsImxlbmd0aCIsInByaXZhY3lQcm90ZWN0aW9uRGV0YWlscyIsInBlcmlvZCIsInByaWNlIiwib3JkZXJJZCIsIm9yZGVyU3RhdHVzIiwiY29udGFjdHMiLCJyZWdpc3RyYW50IiwiY29udGFjdERldGFpbHMiLCJlbWFpbGFkZHIiLCJwaG9uZSIsInRlbG5vY2MiLCJ0ZWxubyIsImFkZHJlc3MiLCJhZGRyZXNzMSIsImNpdHkiLCJjb3VudHJ5IiwiemlwIiwiY29tcGFueSIsImNvbnRhY3RJZCIsImNvbnRhY3RpZCIsImFkbWluIiwidGVjaG5pY2FsIiwidGVjaCIsImJpbGxpbmciLCJjb250YWN0SWRzIiwicHJvZHVjdENhdGVnb3J5IiwicHJvZHVjdEtleSIsImN1c3RvbWVySWQiLCJnZHByIiwibG9ja3MiLCJyYWFWZXJpZmljYXRpb24iLCJkbnNzZWMiLCJhcGlEZXRhaWxzIiwiZG5zUmVjb3JkcyIsInR5cGUiLCJjb250ZW50IiwidHRsIiwiRXJyb3IiLCJhcGlFcnJvciIsIndhcm4iLCJmYWxsYmFja0RvbWFpbiIsImhhbmRsZUF1dG9SZW5ld1RvZ2dsZSIsInZhbHVlIiwiaGFuZGxlUHJpdmFjeVRvZ2dsZSIsImZyb20iLCJ0byIsInJlc3BvbnNlIiwibW9kaWZ5UHJpdmFjeVByb3RlY3Rpb24iLCJwcm90ZWN0UHJpdmFjeSIsInJlYXNvbiIsInN1Y2Nlc3MiLCJkaXYiLCJjbGFzc05hbWUiLCJ2YXJpYW50IiwiZGVmYXVsdFZhbHVlIiwib25DbGljayIsInB1c2giLCJzcGFuIiwid2luZG93Iiwib3BlbiIsImluZGljYXRvclByb3BzIiwiY2hlY2tlZCIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsImNvbG9yIiwiZGlzYWJsZWQiLCJwdXJjaGFzZWQiLCJzaXplIiwiaGFuZGxlUHVyY2hhc2VQcml2YWN5IiwiQXJyYXkiLCJpc0FycmF5IiwibWFwIiwibG9jayIsImluZGV4IiwiZG9tYWluU3RhdHVzIiwicmVnaXN0cmF0aW9uRXJyb3IiLCJucyIsIm9uVXBkYXRlIiwidXBkYXRlZERvbWFpbiIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwidGJvZHkiLCJyZWNvcmQiLCJ0ZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});