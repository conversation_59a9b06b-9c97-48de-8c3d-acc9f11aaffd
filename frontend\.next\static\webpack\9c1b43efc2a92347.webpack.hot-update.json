{"c": ["app/[locale]/layout", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext-intl%5Cdist%5Cesm%5Cshared%5CNextIntlClientProvider.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.jsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.jsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-roboto%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clayout.jsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40fortawesome%5Cfontawesome-svg-core%5Cstyles.css&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Ccomponents%5Chome%5Cheader.jsx&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Ccomponents%5Cshared%5CFloatingButtonsContainer.jsx&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Ccomponents%5Cshared%5Cfooter2.jsx&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Ccomponents%5Cshared%5CwhatsAppFloatingButton.jsx&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Cstyles%5Cglobals.css&server=false!", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40next%5Cthird-parties%5Cdist%5Cgoogle%5Cga.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40next%5Cthird-parties%5Cdist%5Cgoogle%5Cgtm.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40next%5Cthird-parties%5Cdist%5CThirdPartyScriptEmbed.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5C%40react-oauth%5Cgoogle%5Cdist%5Cindex.esm.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5Ccontext%5CAuthContext.jsx&server=false!"]}