"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/page.jsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.jsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_home_homeBanner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst HPackagesPlanGrid = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_hosting_hPackagesPlanGrid_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/hosting/hPackagesPlanGrid */ \"(app-pages-browser)/./src/components/hosting/hPackagesPlanGrid.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/hosting/hPackagesPlanGrid\"\n        ]\n    },\n    ssr: false\n});\n_c = HPackagesPlanGrid;\nconst Services = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_home_services_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/home/<USER>"\n        ]\n    },\n    ssr: false\n});\n_c1 = Services;\nconst OtherServices = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_home_otherServices_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/home/<USER>"\n        ]\n    },\n    ssr: false\n});\n_c2 = OtherServices;\n// const AboutUs = dynamic(() => import(\"../../components/home/<USER>"), {\n//   ssr: false,\n// });\n// const WhyChooseUs = dynamic(() => import(\"../../components/home/<USER>"), {\n//   ssr: false,\n// });\nconst ContactForm2 = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_shared_contactForm2_jsx-_b4f30\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/shared/contactForm2 */ \"(app-pages-browser)/./src/components/shared/contactForm2.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/shared/contactForm2\"\n        ]\n    },\n    ssr: false\n});\n_c3 = ContactForm2;\nconst FAQ2 = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_shared_faq2_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/shared/faq2 */ \"(app-pages-browser)/./src/components/shared/faq2.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/shared/faq2\"\n        ]\n    },\n    ssr: false\n});\n_c4 = FAQ2;\nconst TestimonialsSection = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_home_TestimonialsSection_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/home/<USER>"\n        ]\n    },\n    ssr: false\n});\n_c5 = TestimonialsSection;\nconst CloudMaroc = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_home_cloudMoroc_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/home/<USER>"\n        ]\n    },\n    ssr: false\n});\n_c6 = CloudMaroc;\n// const WhoTrustUs = dynamic(() => import(\"../../components/home/<USER>"), {\n//   ssr: false,\n// });\nconst CompanyIntro = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_home_companyIntro_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx -> \" + \"../../components/home/<USER>"\n        ]\n    },\n    ssr: false\n});\n_c7 = CompanyIntro;\nfunction Home() {\n    _s();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visibleSections, setVisibleSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        HPackagesPlanGrid: true,\n        services: true,\n        otherServices: true,\n        chat: false,\n        testimonials: false,\n        cloud: false,\n        companyIntro: true,\n        trust: false,\n        about: false,\n        whyChoose: false,\n        cloudAdvantages: false,\n        contact: false,\n        faq: false\n    });\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"Home\");\n    // Track scroll position and determine when to load components\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n            const checkVisibility = (id)=>{\n                const element = document.getElementById(id);\n                if (element) {\n                    const rect = element.getBoundingClientRect();\n                    return rect.top <= window.innerHeight && rect.bottom >= 0;\n                }\n                return false;\n            };\n            setVisibleSections((prev)=>({\n                    HPackagesPlanGrid: prev.HPackagesPlanGrid || checkVisibility(\"HPackages-planGrid\"),\n                    services: prev.services || checkVisibility(\"services-section\"),\n                    otherServices: prev.otherServices || checkVisibility(\"other-services-section\"),\n                    chat: prev.chat || checkVisibility(\"chat-section\"),\n                    testimonials: prev.testimonials || checkVisibility(\"testimonials-section\"),\n                    cloud: prev.cloud || checkVisibility(\"cloud-section\"),\n                    companyIntro: prev.companyIntro || checkVisibility(\"company-intro-section\"),\n                    trust: prev.trust || checkVisibility(\"trust-section\"),\n                    about: prev.about || checkVisibility(\"about-section\"),\n                    whyChoose: prev.whyChoose || checkVisibility(\"why-choose-section\"),\n                    cloudAdvantages: prev.cloudAdvantages || checkVisibility(\"cloud-advantages-section\"),\n                    contact: prev.contact || checkVisibility(\"contact-section\"),\n                    faq: prev.faq || checkVisibility(\"faq-section\")\n                }));\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: \"Home\",\n        offerName: \"No offer selected\",\n        id: 0,\n        url: \"/\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full min-h-screen p-0 m-0 font-inter mb-0 bg-gradient-to-b from-white to-transparent \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_homeBanner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                t: t\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto mt-2\",\n                id: \"HPackages-planGrid\",\n                children: visibleSections.HPackagesPlanGrid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HPackagesPlanGrid, {\n                    brandName: \"shared hosting\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"services-section\",\n                children: visibleSections.services && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Services, {\n                    t: t\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 133,\n                    columnNumber: 38\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"other-services-section\",\n                children: visibleSections.otherServices && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OtherServices, {\n                    t: t\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 138,\n                    columnNumber: 43\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"testimonials-section\",\n                children: visibleSections.testimonials && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 144,\n                    columnNumber: 42\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"cloud-section\",\n                children: visibleSections.cloud && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloudMaroc, {\n                    t: t\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 149,\n                    columnNumber: 35\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"company-intro-section\",\n                children: visibleSections.companyIntro && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CompanyIntro, {\n                    t: t\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 154,\n                    columnNumber: 42\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"contact-section\",\n                children: visibleSections.contact && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactForm2, {\n                    data: data,\n                    setData: setData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"faq-section\",\n                children: visibleSections.faq && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FAQ2, {\n                    t: t\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                    lineNumber: 165,\n                    columnNumber: 53\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\page.jsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"aGepqfhjjkquGrLCeq6jhiTUsy8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations\n    ];\n});\n_c8 = Home;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"HPackagesPlanGrid\");\n$RefreshReg$(_c1, \"Services\");\n$RefreshReg$(_c2, \"OtherServices\");\n$RefreshReg$(_c3, \"ContactForm2\");\n$RefreshReg$(_c4, \"FAQ2\");\n$RefreshReg$(_c5, \"TestimonialsSection\");\n$RefreshReg$(_c6, \"CloudMaroc\");\n$RefreshReg$(_c7, \"CompanyIntro\");\n$RefreshReg$(_c8, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/page.jsx\n"));

/***/ })

});