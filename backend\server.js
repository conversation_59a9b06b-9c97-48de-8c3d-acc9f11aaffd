// IMPORTANT: Load global logger FIRST to disable all console logs immediately
require("./utils/globalLogger");

const express = require("express");
const passport = require("passport");
const cors = require("cors");
const http = require("http");
const { Server } = require("socket.io");
const app = express();
const server = http.createServer(app);
const authRouter = require("./routes/authRouter");
const userRouter = require("./routes/userRouter");
const brandRouter = require("./routes/brandRouter");
const packageRouter = require("./routes/packageRouter");
const specificationRouter = require("./routes/specificationRouter");
const categoryRouter = require("./routes/categoryRouter");
const cartRouter = require("./routes/cartRouter");
const orderRouter = require("./routes/orderRouter");
const payzoneRouter = require("./routes/payzoneRouter");
const paymentRouter = require("./routes/paymentRouter");
const chatbotRouter = require("./routes/chatbotRouter");
const adminRouter = require("./routes/adminRouter");
const ticketsRouter = require("./routes/ticketsRouter");
const sslRouter = require("./routes/sslRouter");
const jobRouter = require("./routes/jobRouter");
const interviewRoutes = require("./routes/interviewRoutes");
const contactRouter = require("./routes/contactRouter");
const userContactRouter = require("./routes/userContactRouter");

const dotenv = require("dotenv");
dotenv.config();

// Initialize global logging system FIRST (before any other imports that might use console)
const { initializeGlobalLogging } = require("./utils/globalLogger");
initializeGlobalLogging();

require("./config/db");
require("./services/passport");
const { asyncBackFrontEndLang } = require("./midelwares/sharedMidd");

const i18next = require("./config/i18n");
const i18nextMiddleware = require("i18next-http-middleware");

// Set up i18next middleware for translations
app.use(i18nextMiddleware.handle(i18next)); // Enable i18n middleware

const bodyParser = require("body-parser");
const { isProd } = require("./constants/constant");
app.use(bodyParser.urlencoded({ limit: "10mb", extended: true }));

// Auth error handler
app.use((err, req, res, next) => {
  if (err.name === "AuthenticationError") {
    return res.status(401).json({
      success: false,
      message: err.message || "Authentication failed",
    });
  }
  next(err);
});

// Configure CORS
// const corsOptions = {
//   origin: isProd ? process.env.FRONTEND_URL : process.env.FRONTEND_LOCAL_URL,
//   credentials: true,
// };

// const corsOptions = {
//   origin: function(origin, callback) {
//     const allowedOrigins = [
//       "https://www.ztechengineering.com",
//       isProd ? process.env.FRONTEND_URL : process.env.FRONTEND_LOCAL_URL
//     ];

//     // Allow requests with no origin (like mobile apps, WebViews, or curl requests)
//     if (!origin) return callback(null, true);

//     // Check if origin starts with any of the allowed origins
//     const isAllowed = allowedOrigins.some(allowedOrigin =>
//       origin === allowedOrigin || origin.startsWith(allowedOrigin)
//     );

//     if (isAllowed) {
//       callback(null, true);
//     } else {
//       console.log('Blocked origin:', origin); // For debugging
//       callback(null, false);
//     }
//   },
//   credentials: true,
// };

const allowedOrigins = isProd
  ? [
      "https://ztechengineering.com",
      "https://www.ztechengineering.com",
      "https://ztekengineering.com",
      "https://www.ztekengineering.com",
    ]
  : [process.env.FRONTEND_LOCAL_URL || "http://localhost:3001"];

// Configure CORS
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (e.g., non-browser clients)
    if (!origin) return callback(null, true);

    // Check if origin is in allowed list
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
};

app.use(cors(corsOptions));

// JSON & URL encoding middleware
app.use(
  express.json({
    limit: "10mb",
    verify: (req, res, buf) => {
      req.rawBody = buf.toString(); // Stores the raw request body
    },
  })
);
app.use(express.urlencoded({ limit: "10mb", extended: true }));

// Static file serving
app.use(express.static("public"));
app.use("/images", express.static("public/images"));
app.use("/avatars", express.static("public/avatars"));
app.use("/images/uploads", express.static("public/images/uploads"));

const cookieParser = require("cookie-parser");
const domainMngRouter = require("./routes/domainMngRouter");
app.use(cookieParser());
app.use(passport.initialize());

// Apply the asyncBackFrontEndLang middleware globally
app.use(asyncBackFrontEndLang);

// Routes
app.use("/auth", authRouter);
app.use("/user", userRouter);
app.use("/category", categoryRouter);
app.use("/brand", brandRouter);
app.use("/package", packageRouter);
app.use("/specification", specificationRouter);
app.use("/cart", cartRouter);
app.use("/order", orderRouter);
app.use("/payment", payzoneRouter);
app.use("/admin", adminRouter);
app.use("/paymentHistory", paymentRouter);
app.use("/tickets", ticketsRouter);
app.use("/chatbot", chatbotRouter);
app.use("/ssl", sslRouter);
app.use("/jobs", jobRouter);
app.use("/admin/interviews", interviewRoutes);
app.use("/domainMng", domainMngRouter);
app.use("/contact", contactRouter);
app.use("/user-contact", userContactRouter);

app.get("/", (req, res) => {
  res.redirect(process.env.FRONTEND_URL);
});

// Public route for site settings (for SEO metadata)
app.get("/api/public/site-settings", async (req, res) => {
  try {
    const SiteSettings = require("./models/SiteSettings");
    const settings = await SiteSettings.getSettings();

    // Return only public information needed for SEO
    const publicSettings = {
      general: {
        siteName: settings.general?.siteName || "ZtechEngineering",
        siteDescription:
          settings.general?.siteDescription ||
          "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
      },
      seo: {
        defaultTitle:
          settings.seo?.defaultTitle ||
          "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
        defaultDescription:
          settings.seo?.defaultDescription ||
          "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
        defaultKeywords:
          settings.seo?.defaultKeywords ||
          "web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering",
        favicon: settings.seo?.favicon || "/favicon.png",
        googleTagManagerId: settings.seo?.googleTagManagerId || "GTM-WBVG4FCK",
        googleSiteVerification:
          settings.seo?.googleSiteVerification ||
          "RokYIbdh-kKoq7cMq7qJURkC43dc7JgI3ojch4CL0RQ",
        bingVerification: settings.seo?.bingVerification || "",
      },
    };

    return res.status(200).json({
      success: true,
      data: publicSettings,
    });
  } catch (error) {
    console.error("Error fetching public site settings:", error);
    // Return default values if database fails
    return res.status(200).json({
      success: true,
      data: {
        general: {
          siteName: "ZtechEngineering",
          siteDescription:
            "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
        },
        seo: {
          defaultTitle:
            "ZtechEngineering | Web & Mobile Development, Cloud Hosting in Morocco",
          defaultDescription:
            "ZtechEngineering offers web development, mobile apps, and cloud hosting in Morocco. Innovative solutions for your business success.",
          defaultKeywords:
            "web development Morocco, mobile applications Morocco, cloud hosting Morocco, digital agency, ZtechEngineering",
          favicon: "/favicon.png",
          googleTagManagerId: "GTM-WBVG4FCK",
          googleSiteVerification: "RokYIbdh-kKoq7cMq7qJURkC43dc7JgI3ojch4CL0RQ",
          bingVerification: "",
        },
      },
    });
  }
});

// Socket.IO test route
app.get("/socket-test", (req, res) => {
  res.sendFile(__dirname + "/public/socket-test.html");
});

// Import the chatbot cache service
const chatbotCacheService = require("./services/chatbotCacheService");

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: function (origin, callback) {
      // Allow requests with no origin (e.g., non-browser clients)
      if (!origin) return callback(null, true);

      // In development, allow all origins for easier testing
      if (!isProd) {
        console.log("Socket.IO CORS: Allowing origin in dev mode:", origin);
        callback(null, true);
        return;
      }

      // Check if origin is in allowed list
      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.log("Socket.IO CORS: Blocked origin:", origin);
        // For development, we'll still allow all origins
        callback(null, true);
        // In strict production, you might want to restrict:
        // callback(new Error("Not allowed by CORS"));
      }
    },
    credentials: true,
  },
  // Explicitly set the transports
  transports: ["websocket", "polling"],
  // Add path for clarity
  path: "/socket.io/",
  // Increase ping timeout to prevent disconnections
  pingTimeout: 60000,
  // Connection timeout
  connectTimeout: 45000,
});

// Create a socket service instance
const socketService = require("./services/socketService")(io);

// Make socket.io instance available globally
global.io = io;

// Import and start the abandoned cart service
const {
  startAbandonedCartCronJob,
} = require("./services/abandonedCartService");
const {
  initializeCustomNotificationService,
} = require("./services/customNotificationService");

const PORT = process.env.PORT || 5002;

// Variable to track if this is the first time the server is starting
// This prevents nodemon from triggering the cache update multiple times
let isFirstStart = true;

server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);

  // Only initialize the cache on the first start, not on nodemon restarts
  if (isFirstStart) {
    isFirstStart = false;

    // Delay the cache initialization to avoid immediate restart
    setTimeout(() => {
      // Initialize the package cache on server start
      chatbotCacheService
        .updateCache()
        .then(() =>
          console.log("[CHATBOT CACHE] Initial package cache created")
        )
        .catch((err) =>
          console.error(
            "[CHATBOT CACHE] Failed to create initial package cache:",
            err
          )
        );

      // Set up scheduled cache update every 24 hours
      // Using setInterval inside setTimeout to prevent immediate execution
      setInterval(() => {
        chatbotCacheService
          .updateCache()
          .then(() => console.log("[CHATBOT CACHE] Package cache updated"))
          .catch((err) =>
            console.error(
              "[CHATBOT CACHE] Failed to update package cache:",
              err
            )
          );
      }, 24 * 60 * 60 * 1000); // 24 hours

      // Start the abandoned cart cron job
      startAbandonedCartCronJob()
        .then((job) => {
          if (job) {
            console.log("[ABANDONED CART] Cron job started successfully");
          } else {
            console.log(
              "[ABANDONED CART] Cron job not started (disabled in settings)"
            );
          }
        })
        .catch((err) => {
          console.error("[ABANDONED CART] Error starting cron job:", err);
        });

      // Initialize custom notification service
      initializeCustomNotificationService()
        .then(() => {
          console.log("[CUSTOM NOTIFICATION] Service initialized successfully");
        })
        .catch((err) => {
          console.error(
            "[CUSTOM NOTIFICATION] Error initializing service:",
            err
          );
        });
    }, 5000); // 5 second delay
  }
});
