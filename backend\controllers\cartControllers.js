const Cart = require("../models/Cart");
const Package = require("../models/Package");

exports.getCart = async (req, res) => {
  try {
    console.log("user cart", req.user._id);
    const { user } = req;

    if (!user) {
      return res
        .status(201)
        .json({ message: "User not authenticated OR Guest" });
    }

    // Fetch the cart and populate the related packages
    const cart = await Cart.findOne({ user: user._id }).populate({
      path: "items.package", // Only populate package field for package items
      select:
        "name reference per price regularPrice image brand name_fr discounts",
      populate: {
        path: "brand",
        select: "name name_fr category",
        populate: {
          path: "category",
          select: "name description",
        },
      },
    });

    if (!cart) {
      return res.status(200).json({
        success: true,
        cart: { items: [], totalPrice: 0, totalDiscount: 0, cartCount: 0 },
      });
    }

    res.status(200).json({
      success: true,
      cart,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Update an item's period in the cart
exports.updateItemPeriod = async (req, res) => {
  const { packageId, period } = req.body;
  try {
    // Find the user's cart
    const cart = await Cart.findOne({ user: req.user._id }).populate({
      path: "items.package",
      select: "name reference per price regularPrice image price",
    });

    if (!cart) {
      return res.status(404).json({ message: "Cart not found" });
    }

    // Find the item in the cart
    const existingItemIndex = cart.items.findIndex(
      (item) => item.package?._id.toString() === packageId.toString()
    );

    if (existingItemIndex === -1) {
      return res.status(404).json({ message: "Item not found in cart" });
    }

    // Update the period
    cart.items[existingItemIndex].period = period;

    // Recalculate the item price based on the discount
    const package = await Package.findById(packageId);

    if (!package) {
      return res.status(404).json({ message: "Package not found" });
    }

    const discountEntry = package.discounts.find(
      (d) => d.period === period && d.period > 0
    );
    console.log("discountEntryeee: ", discountEntry);

    const discountRate = discountEntry?.percentage || 0; // Default to 0 if period not found

    // Update the  discount
    cart.items[existingItemIndex].discount =
      (discountRate / 100) *
      (package.price * cart.items[existingItemIndex].quantity * period);

    // Save the cart
    await cart.save();

    res.status(200).json({
      message: "Item period updated",
      success: true,
      cart,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Add an item to the cart
exports.addItemToCart = async (req, res) => {
  const { packageId, quantity = 1, period = 1 } = req.body;
  console.log("Adding item to cart: body= ", req.body);
  try {
    // Find the package details
    const package = await Package.findById(packageId);
    if (!package) {
      return res.status(404).json({ message: req.t("error.plan_not_found") });
    }

    // Find the user's cart or create a new
    let cart = await Cart.findOne({ user: req.user?._id }).populate({
      path: "items.package",
      select: "name reference per price regularPrice image",
    });

    if (!cart) {
      cart = new Cart({ user: req.user?._id, items: [] });
    }

    // Add the package to the cart
    await cart.addItem(package, quantity, period);
    // package.price,

    // Save the cart
    res.status(200).json({
      message: "Item added to cart",
      success: true,
      cart,
    });
  } catch (error) {
    if (error.code) {
      // Handle known errors
      console.error(error.message);
      return res
        .status(error.code === "MAX_QUANTITY_REACHED" ? 400 : 404)
        .json({
          success: false,
          message: req.t(error.message),
          code: error.code,
        });
    }

    // Handle unexpected errors
    console.error("Unexpected error:", error);
    res.status(500).json({ message: req.t("error_generic") });
  }
};

// Remove an item from the cart
exports.removeItemFromCart = async (req, res) => {
  const { packageId, quantity = 1 } = req.body;
  try {
    console.log("Removing item from cart: ", packageId, quantity);
    // Find the user's cart
    const cart = await Cart.findOne({ user: req.user._id }).populate({
      path: "items.package",
      select: "name reference per price regularPrice image",
    });

    if (!cart) {
      return res.status(404).json({ message: "Cart not found" });
    }

    // Remove the package from the cart
    await cart.removeItem(packageId, quantity);

    // Save the cart
    res.status(200).json({
      message: "Item removed from cart",
      success: true,
      cart,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Clear the cart
exports.clearCart = async (req, res) => {
  try {
    const cart = await Cart.findOne({ user: req.user._id });
    if (!cart) {
      return res.status(404).json({ message: "Cart not found" });
    }

    // Clear the cart
    await cart.clearCart();
    res.status(200).json({ message: "Cart cleared" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Remove domain from cart
exports.removeDomainFromCart = async (req, res) => {
  try {
    const { itemId } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    const cart = await Cart.findOne({ user: userId });
    if (!cart) {
      return res.status(404).json({ message: "Cart not found" });
    }

    // Find and remove the domain item
    const itemIndex = cart.items.findIndex(
      (item) => item._id.toString() === itemId
    );

    if (itemIndex === -1) {
      return res.status(404).json({ message: "Item not found in cart" });
    }

    // Remove the item
    cart.items.splice(itemIndex, 1);
    await cart.save();

    res.status(200).json({
      success: true,
      message: "Domain removed from cart",
      cart,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Update domain period
exports.updateDomainPeriod = async (req, res) => {
  try {
    const { itemId, period } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    const cart = await Cart.findOne({ user: userId });
    if (!cart) {
      return res.status(404).json({ message: "Cart not found" });
    }

    // Find the domain item
    const item = cart.items.find((item) => item._id.toString() === itemId);

    if (!item) {
      return res.status(404).json({ message: "Item not found in cart" });
    }

    // Update the period
    item.period = parseInt(period, 10);

    // Recalculate price based on the new period if raw pricing data is available
    if (item.rawPricing && item.rawPricing.addnewdomain) {
      const pricePerYear = item.rawPricing.addnewdomain[period.toString()];
      if (pricePerYear) {
        // For domains, the total price should be price per year * period
        item.price = parseFloat(pricePerYear) * parseInt(period, 10);
        console.log(
          `Domain price updated: ${pricePerYear} per year * ${period} years = ${item.price}`
        );
      }
    }

    await cart.save();

    res.status(200).json({
      success: true,
      message: "Domain period updated",
      cart,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Update domain options (privacy protection, auto-renewal)
exports.updateDomainOptions = async (req, res) => {
  try {
    const { itemId, privacyProtection, autoRenew } = req.body;
    const userId = req.user?._id;

    console.log(`🔒 [CART] Updating domain options for item ${itemId}:`, {
      privacyProtection,
      autoRenew,
      userId,
    });

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    const cart = await Cart.findOne({ user: userId });
    if (!cart) {
      return res.status(404).json({ message: "Cart not found" });
    }

    // Find the domain item
    const item = cart.items.find((item) => item._id.toString() === itemId);

    if (!item) {
      return res.status(404).json({ message: "Item not found in cart" });
    }

    if (item.type !== "domain") {
      return res.status(400).json({ message: "Item is not a domain" });
    }

    console.log(`🔒 [CART] Found domain item ${item.domainName}:`, {
      currentPrivacyProtection: item.privacyProtection,
      currentAutoRenew: item.autoRenew,
      newPrivacyProtection: privacyProtection,
      newAutoRenew: autoRenew,
    });

    // Update the domain options
    if (typeof privacyProtection === "boolean") {
      item.privacyProtection = privacyProtection;
      console.log(
        `🔒 [CART] ✅ Updated privacy protection for ${item.domainName}: ${privacyProtection}`
      );
    }
    if (typeof autoRenew === "boolean") {
      item.autoRenew = autoRenew;
      console.log(
        `🔒 [CART] ✅ Updated auto-renew for ${item.domainName}: ${autoRenew}`
      );
    }

    await cart.save();

    console.log(
      `🔒 [CART] ✅ Cart saved successfully for domain ${item.domainName}`
    );

    res.status(200).json({
      success: true,
      message: "Domain options updated",
      cart,
    });
  } catch (error) {
    console.error("🔒 [CART] ❌ Error updating domain options:", error);
    res.status(500).json({ message: "Server error" });
  }
};
