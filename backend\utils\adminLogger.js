const AdminActivityLog = require("../models/AdminActivityLog");
const logger = require("./globalLogger")("adminLogger");

/**
 * Utility to log admin activities from anywhere in the code
 * @param {string} adminId - The ID of the admin performing the action
 * @param {string} action - The action being performed (CREATE, UPDATE, DELETE, VIEW, etc.)
 * @param {string} targetModel - The model being affected
 * @param {string|object} target - The target ID or object
 * @param {object} details - Additional details about the action
 * @returns {Promise<boolean>} - Whether the logging was successful
 */
const logActivity = async (adminId, action, targetModel, target, details) => {
  try {
    if (!adminId) {
      logger.warn("Admin ID is required for activity logging");
      return false;
    }

    const log = new AdminActivityLog({
      admin: adminId,
      action,
      targetModel,
      target,
      details,
    });

    await log.save();
    return true;
  } catch (error) {
    console.error("Failed to log admin activity:", error);
    return false;
  }
};

/**
 * Sanitize object for logging by removing sensitive fields
 * @param {object} obj - The object to sanitize
 * @returns {object} - The sanitized object
 */
const sanitizeForLogging = (obj) => {
  if (!obj) return obj;

  // Create a copy to avoid modifying the original
  const sanitized = { ...obj };

  // Remove sensitive fields
  const sensitiveFields = [
    "password",
    "hashed_password",
    "salt",
    "hashedUniqueString",
    "hashedUniqueStringVerifyEmail",
    "hashedUniqueStringResetPassword",
  ];

  sensitiveFields.forEach((field) => {
    if (sanitized[field] !== undefined) {
      delete sanitized[field];
    }
  });

  return sanitized;
};

module.exports = {
  // Generic log method for any action
  log: (adminId, action, targetModel, target, details) =>
    logActivity(
      adminId,
      action,
      targetModel,
      target,
      sanitizeForLogging(details)
    ),

  logActivity,
  sanitizeForLogging,

  // Convenience methods for standard CRUD operations
  logCreate: (adminId, targetModel, target, details) =>
    logActivity(
      adminId,
      "CREATE",
      targetModel,
      target,
      sanitizeForLogging(details)
    ),

  logUpdate: (adminId, targetModel, target, details) =>
    logActivity(
      adminId,
      "UPDATE",
      targetModel,
      target,
      sanitizeForLogging(details)
    ),

  logDelete: (adminId, targetModel, target, details) =>
    logActivity(
      adminId,
      "DELETE",
      targetModel,
      target,
      sanitizeForLogging(details)
    ),

  logView: (adminId, targetModel, target, details) =>
    logActivity(
      adminId,
      "VIEW",
      targetModel,
      target,
      sanitizeForLogging(details)
    ),

  // Convenience methods for user management
  logCreateUser: (adminId, target, details) =>
    logActivity(
      adminId,
      "CREATE_USER",
      "User",
      target,
      sanitizeForLogging(details)
    ),

  logUpdateUser: (adminId, target, details) =>
    logActivity(
      adminId,
      "UPDATE_USER",
      "User",
      target,
      sanitizeForLogging(details)
    ),

  logDeleteUser: (adminId, target, details) =>
    logActivity(
      adminId,
      "DELETE_USER",
      "User",
      target,
      sanitizeForLogging(details)
    ),

  // Convenience methods for other actions
  logLogin: (adminId, details) =>
    logActivity(adminId, "LOGIN", "Auth", adminId, sanitizeForLogging(details)),

  logLogout: (adminId, details) =>
    logActivity(
      adminId,
      "LOGOUT",
      "Auth",
      adminId,
      sanitizeForLogging(details)
    ),

  logExport: (adminId, targetModel, details) =>
    logActivity(
      adminId,
      "EXPORT",
      targetModel,
      null,
      sanitizeForLogging(details)
    ),

  logImport: (adminId, targetModel, details) =>
    logActivity(
      adminId,
      "IMPORT",
      targetModel,
      null,
      sanitizeForLogging(details)
    ),

  logChangeStatus: (adminId, targetModel, target, details) =>
    logActivity(
      adminId,
      "CHANGE_STATUS",
      targetModel,
      target,
      sanitizeForLogging(details)
    ),

  // logViewDashboard: (adminId, details) =>
  //   logActivity(
  //     adminId,
  //     "VIEW_DASHBOARD_STATS",
  //     "Dashboard",
  //     null,
  //     sanitizeForLogging(details)
  //   ),

  // Convenience methods for ticket management
  logCreateTicket: (adminId, target, details) =>
    logActivity(
      adminId,
      "CREATE_TICKET",
      "Ticket",
      target,
      sanitizeForLogging(details)
    ),

  logUpdateTicket: (adminId, target, details) =>
    logActivity(
      adminId,
      "UPDATE_TICKET",
      "Ticket",
      target,
      sanitizeForLogging(details)
    ),

  logResolveTicket: (adminId, target, details) =>
    logActivity(
      adminId,
      "RESOLVE_TICKET",
      "Ticket",
      target,
      sanitizeForLogging(details)
    ),

  logCloseTicket: (adminId, target, details) =>
    logActivity(
      adminId,
      "CLOSE_TICKET",
      "Ticket",
      target,
      sanitizeForLogging(details)
    ),
};
