"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx":
/*!*******************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/page.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Globe,Lock,Mail,RefreshCw,Server,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_PrivacyProtectionManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/PrivacyProtectionManager */ \"(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DomainDetailPage(param) {\n    let { params } = param;\n    var _domain_status, _domain_status1, _domain_status2, _domain_status3, _domain_status4, _domain_status5, _domain_status6;\n    _s();\n    const { id } = params;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Utility function to format Unix timestamps\n    const formatDate = (unixTimestamp)=>{\n        if (!unixTimestamp) return \"Not available\";\n        try {\n            const date = new Date(parseInt(unixTimestamp) * 1000);\n            return date.toLocaleDateString(\"en-US\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } catch (error) {\n            return \"Invalid date\";\n        }\n    };\n    // Utility function to format domain status\n    const formatStatus = (status)=>{\n        if (!status) return \"unknown\";\n        return status.toLowerCase().replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getDomainDetails = async ()=>{\n            try {\n                var _domainsRes_data;\n                setLoading(true);\n                // First, get the user's domains to find the domain name by ID\n                const domainsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                const userDomains = ((_domainsRes_data = domainsRes.data) === null || _domainsRes_data === void 0 ? void 0 : _domainsRes_data.domains) || [];\n                // Find the domain with the matching ID\n                const userDomain = userDomains.find((d)=>d.id === id);\n                if (!userDomain) {\n                    console.error(\"Domain not found with ID:\", id);\n                    setLoading(false);\n                    return;\n                }\n                console.log(\"Found user domain:\", userDomain);\n                // Try to get detailed information from the reseller API\n                try {\n                    var _detailsRes_data;\n                    console.log(\"\\uD83D\\uDD0D Fetching real domain details for:\", userDomain.name);\n                    // Get real domain details from reseller API\n                    const detailsRes = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetailsByName(userDomain.name, \"All\" // Get all available details\n                    );\n                    console.log(\"✅ Real domain details from reseller API:\", detailsRes.data);\n                    const apiDomain = (_detailsRes_data = detailsRes.data) === null || _detailsRes_data === void 0 ? void 0 : _detailsRes_data.domain;\n                    if (apiDomain) {\n                        var _apiDomain_privacyProtection, _apiDomain_contactDetails, _apiDomain_contactDetails1, _apiDomain_contactDetails2, _apiDomain_contactDetails3;\n                        // Use real data from reseller API\n                        const combinedDomain = {\n                            id: userDomain.id,\n                            name: apiDomain.domainName || userDomain.name,\n                            status: apiDomain.status || userDomain.status,\n                            registrationDate: apiDomain.registrationDate || userDomain.registrationDate,\n                            expiryDate: apiDomain.expiryDate || userDomain.expiryDate,\n                            autoRenew: apiDomain.autoRenew || userDomain.autoRenew || false,\n                            registrar: \"ZTech Domains\",\n                            // Use real nameservers from API\n                            nameservers: apiDomain.nameservers && apiDomain.nameservers.length > 0 ? apiDomain.nameservers : userDomain.nameservers || [\n                                \"ns1.ztech\",\n                                \"ns2.ztech\",\n                                \"ns3.ztech\",\n                                \"ns4.ztech\"\n                            ],\n                            // Use real privacy protection data\n                            privacyProtection: ((_apiDomain_privacyProtection = apiDomain.privacyProtection) === null || _apiDomain_privacyProtection === void 0 ? void 0 : _apiDomain_privacyProtection.enabled) || userDomain.privacyProtection || false,\n                            privacyProtectionDetails: apiDomain.privacyProtection,\n                            period: userDomain.period,\n                            price: userDomain.price,\n                            orderId: apiDomain.orderId || userDomain.orderId,\n                            orderStatus: apiDomain.orderStatus || userDomain.orderStatus,\n                            // Real contact details from API\n                            contacts: {\n                                registrant: ((_apiDomain_contactDetails = apiDomain.contactDetails) === null || _apiDomain_contactDetails === void 0 ? void 0 : _apiDomain_contactDetails.registrant) ? {\n                                    name: apiDomain.contactDetails.registrant.name,\n                                    email: apiDomain.contactDetails.registrant.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.registrant.telnocc, \" \").concat(apiDomain.contactDetails.registrant.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.registrant.address1, \", \").concat(apiDomain.contactDetails.registrant.city, \", \").concat(apiDomain.contactDetails.registrant.country, \" \").concat(apiDomain.contactDetails.registrant.zip),\n                                    company: apiDomain.contactDetails.registrant.company,\n                                    contactId: apiDomain.contactDetails.registrant.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                admin: ((_apiDomain_contactDetails1 = apiDomain.contactDetails) === null || _apiDomain_contactDetails1 === void 0 ? void 0 : _apiDomain_contactDetails1.admin) ? {\n                                    name: apiDomain.contactDetails.admin.name,\n                                    email: apiDomain.contactDetails.admin.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.admin.telnocc, \" \").concat(apiDomain.contactDetails.admin.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.admin.address1, \", \").concat(apiDomain.contactDetails.admin.city, \", \").concat(apiDomain.contactDetails.admin.country, \" \").concat(apiDomain.contactDetails.admin.zip),\n                                    company: apiDomain.contactDetails.admin.company,\n                                    contactId: apiDomain.contactDetails.admin.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                technical: ((_apiDomain_contactDetails2 = apiDomain.contactDetails) === null || _apiDomain_contactDetails2 === void 0 ? void 0 : _apiDomain_contactDetails2.tech) ? {\n                                    name: apiDomain.contactDetails.tech.name,\n                                    email: apiDomain.contactDetails.tech.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.tech.telnocc, \" \").concat(apiDomain.contactDetails.tech.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.tech.address1, \", \").concat(apiDomain.contactDetails.tech.city, \", \").concat(apiDomain.contactDetails.tech.country, \" \").concat(apiDomain.contactDetails.tech.zip),\n                                    company: apiDomain.contactDetails.tech.company,\n                                    contactId: apiDomain.contactDetails.tech.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                },\n                                billing: ((_apiDomain_contactDetails3 = apiDomain.contactDetails) === null || _apiDomain_contactDetails3 === void 0 ? void 0 : _apiDomain_contactDetails3.billing) ? {\n                                    name: apiDomain.contactDetails.billing.name,\n                                    email: apiDomain.contactDetails.billing.emailaddr,\n                                    phone: \"+\".concat(apiDomain.contactDetails.billing.telnocc, \" \").concat(apiDomain.contactDetails.billing.telno),\n                                    address: \"\".concat(apiDomain.contactDetails.billing.address1, \", \").concat(apiDomain.contactDetails.billing.city, \", \").concat(apiDomain.contactDetails.billing.country, \" \").concat(apiDomain.contactDetails.billing.zip),\n                                    company: apiDomain.contactDetails.billing.company,\n                                    contactId: apiDomain.contactDetails.billing.contactid\n                                } : {\n                                    name: \"Contact information not available\",\n                                    email: \"Contact information not available\",\n                                    phone: \"Contact information not available\",\n                                    address: \"Contact information not available\"\n                                }\n                            },\n                            // Contact IDs for API operations\n                            contactIds: apiDomain.contacts,\n                            // Additional real data from API\n                            productCategory: apiDomain.productCategory,\n                            productKey: apiDomain.productKey,\n                            customerId: apiDomain.customerId,\n                            gdpr: apiDomain.gdpr,\n                            locks: apiDomain.locks,\n                            raaVerification: apiDomain.raaVerification,\n                            dnssec: apiDomain.dnssec,\n                            // Raw API response for debugging\n                            apiDetails: apiDomain,\n                            // Default DNS records (placeholder - would need separate API call)\n                            dnsRecords: [\n                                {\n                                    id: \"rec1\",\n                                    type: \"A\",\n                                    name: \"@\",\n                                    content: \"DNS records available via separate API\",\n                                    ttl: 3600\n                                }\n                            ]\n                        };\n                        setDomain(combinedDomain);\n                    } else {\n                        throw new Error(\"No domain data received from API\");\n                    }\n                } catch (apiError) {\n                    console.warn(\"Could not fetch domain details from API:\", apiError);\n                    // Fallback to user domain data only\n                    const fallbackDomain = {\n                        id: userDomain.id,\n                        name: userDomain.name,\n                        status: userDomain.status,\n                        registrationDate: userDomain.registrationDate,\n                        expiryDate: userDomain.expiryDate,\n                        autoRenew: userDomain.autoRenew,\n                        registrar: userDomain.registrar || \"ZTech Domains\",\n                        nameservers: userDomain.nameservers || [\n                            \"ns1.ztech\",\n                            \"ns2.ztech\",\n                            \"ns3.ztech\",\n                            \"ns4.ztech\"\n                        ],\n                        privacyProtection: userDomain.privacyProtection,\n                        period: userDomain.period,\n                        price: userDomain.price,\n                        orderId: userDomain.orderId,\n                        orderStatus: userDomain.orderStatus,\n                        contacts: {\n                            registrant: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            admin: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            },\n                            technical: {\n                                name: \"Contact information not available\",\n                                email: \"Contact information not available\",\n                                phone: \"Contact information not available\",\n                                address: \"Contact information not available\"\n                            }\n                        },\n                        dnsRecords: [\n                            {\n                                id: \"rec1\",\n                                type: \"A\",\n                                name: \"@\",\n                                content: \"DNS information not available\",\n                                ttl: 3600\n                            }\n                        ]\n                    };\n                    setDomain(fallbackDomain);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domain details\", error);\n                setLoading(false);\n            }\n        };\n        getDomainDetails();\n    }, [\n        id\n    ]);\n    const handleAutoRenewToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.toggleAutoRenewal(id, value);\n            setDomain({\n                ...domain,\n                autoRenew: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling auto renewal\", error);\n        }\n    };\n    const handlePrivacyToggle = async (value)=>{\n        try {\n            // This would be replaced with actual API call when implemented\n            // await domainMngService.togglePrivacyProtection(id, value);\n            setDomain({\n                ...domain,\n                privacyProtection: value\n            });\n        } catch (error) {\n            console.error(\"Error toggling privacy protection\", error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, this);\n    }\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                    variant: \"h4\",\n                    className: \"text-gray-800 font-bold mb-2\",\n                    children: t(\"domain_not_found\", {\n                        defaultValue: \"Domain Not Found\"\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    className: \"mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 316,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            variant: \"h1\",\n                                            className: \"text-2xl font-bold text-gray-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize mr-2 \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : domain.status === \"expired\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: dt(domain.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        dt(\"registrar\"),\n                                                        \": \",\n                                                        domain.registrar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outlined\",\n                                    className: \"border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2\",\n                                    onClick: ()=>window.open(\"http://\".concat(domain.name), \"_blank\"),\n                                    children: [\n                                        t(\"visit_website\", {\n                                            defaultValue: \"Visit Website\"\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                    onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/renew\")),\n                                    children: [\n                                        dt(\"renew_domain\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                    value: activeTab,\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                            className: \"bg-gray-100 rounded-lg p-1\",\n                            indicatorProps: {\n                                className: \"bg-white shadow-md rounded-md\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"overview\",\n                                    onClick: ()=>setActiveTab(\"overview\"),\n                                    className: activeTab === \"overview\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"overview\", {\n                                        defaultValue: \"Overview\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"dns\",\n                                    onClick: ()=>setActiveTab(\"dns\"),\n                                    className: activeTab === \"dns\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"dns_settings\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"contacts\",\n                                    onClick: ()=>setActiveTab(\"contacts\"),\n                                    className: activeTab === \"contacts\" ? \"text-blue-600\" : \"\",\n                                    children: dt(\"domain_contacts\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                    value: \"privacy\",\n                                    onClick: ()=>setActiveTab(\"privacy\"),\n                                    className: activeTab === \"privacy\" ? \"text-blue-600\" : \"\",\n                                    children: t(\"privacy\", {\n                                        defaultValue: \"Privacy\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"overview\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"domain_details\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"domain_name\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium\",\n                                                                            children: domain.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Order ID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-blue-600\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                domain.orderId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"status\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(((_domain_status = domain.status) === null || _domain_status === void 0 ? void 0 : _domain_status.toLowerCase()) === \"active\" ? \"bg-green-100 text-green-800\" : ((_domain_status1 = domain.status) === null || _domain_status1 === void 0 ? void 0 : _domain_status1.toLowerCase()) === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : ((_domain_status2 = domain.status) === null || _domain_status2 === void 0 ? void 0 : _domain_status2.toLowerCase()) === \"expired\" ? \"bg-red-100 text-red-800\" : ((_domain_status3 = domain.status) === null || _domain_status3 === void 0 ? void 0 : _domain_status3.toLowerCase()) === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                            children: dt(((_domain_status4 = domain.status) === null || _domain_status4 === void 0 ? void 0 : _domain_status4.toLowerCase()) || \"unknown\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 458,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"registration_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.registrationDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"expiry_date\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: formatDate(domain.expiryDate)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"auto_renew\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                            checked: domain.autoRenew,\n                                                                            onChange: (e)=>handleAutoRenewToggle(e.target.checked),\n                                                                            color: \"blue\",\n                                                                            disabled: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: \"Security & Protection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: dt(\"whois_privacy\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                    checked: domain.privacyProtection,\n                                                                                    onChange: (e)=>handlePrivacyToggle(e.target.checked),\n                                                                                    color: \"blue\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 518,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded \".concat(domain.privacyProtection ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                                    children: domain.privacyProtection ? \"Enabled\" : \"Disabled\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                domain.orderStatus && Array.isArray(domain.orderStatus) && domain.orderStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Domain Locks\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.orderStatus.map((lock, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded\",\n                                                                                    children: lock\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.domainStatus && Array.isArray(domain.domainStatus) && domain.domainStatus.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registry Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: domain.domainStatus.map((status, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                                                    children: status\n                                                                                }, index, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 556,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.raaVerification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"RAA Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 566,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.raaVerification.status === \"Verified\" ? \"bg-green-100 text-green-800\" : domain.raaVerification.status === \"Pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                            children: domain.raaVerification.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                domain.gdpr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"GDPR Protection\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded \".concat(domain.gdpr.enabled === \"true\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-600\"),\n                                                                            children: domain.gdpr.enabled === \"true\" ? \"Enabled\" : \"Disabled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                (((_domain_status5 = domain.status) === null || _domain_status5 === void 0 ? void 0 : _domain_status5.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" || domain.registrationError) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Registration Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 599,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-100 text-red-800 rounded\",\n                                                                                    children: \"Registration Failed\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 603,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                domain.registrationError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 bg-red-50 text-red-700 rounded\",\n                                                                                    children: domain.registrationError\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 607,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                            children: dt(\"nameservers\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                domain.nameservers && Array.isArray(domain.nameservers) && domain.nameservers.length > 0 ? domain.nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"NS \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 631,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                                className: \"font-medium\",\n                                                                                children: ns\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_domain_status6 = domain.status) === null || _domain_status6 === void 0 ? void 0 : _domain_status6.toLowerCase()) === \"failed\" || domain.orderStatus === \"FAILED\" ? \"Nameservers not available - Registration failed\" : \"No nameservers configured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 638,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outlined\",\n                                                                        className: \"w-full border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                                                        onClick: ()=>setActiveTab(\"dns\"),\n                                                                        children: dt(\"update_nameservers\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"dns\",\n                                    className: \"p-0 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: (updatedDomain)=>setDomain(updatedDomain)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: dt(\"manage_dns_records\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/add\")),\n                                                                children: t(\"add_record\", {\n                                                                    defaultValue: \"Add Record\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"type\", {\n                                                                                    defaultValue: \"Type\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 691,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"name\", {\n                                                                                    defaultValue: \"Name\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 694,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"content\", {\n                                                                                    defaultValue: \"Content\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                                children: t(\"ttl\", {\n                                                                                    defaultValue: \"TTL\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 700,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-500\",\n                                                                                children: t(\"actions\", {\n                                                                                    defaultValue: \"Actions\"\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                lineNumber: 703,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    className: \"divide-y divide-gray-200\",\n                                                                    children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"hover:bg-gray-50\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                                    children: record.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 714,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.content\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 717,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                    children: record.ttl\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 720,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"px-4 py-3 text-right\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        variant: \"text\",\n                                                                                        className: \"text-blue-600\",\n                                                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/dns/\").concat(record.id)),\n                                                                                        children: t(\"edit\", {\n                                                                                            defaultValue: \"Edit\"\n                                                                                        })\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                        lineNumber: 724,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                                    lineNumber: 723,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, record.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"contacts\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: dt(\"domain_contacts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"registrant\", {\n                                                                        defaultValue: \"Registrant Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: domain.contacts.registrant.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.registrant.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-blue-600 font-medium\",\n                                                                            children: domain.contacts.registrant.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 765,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600 mt-2\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCE7 \",\n                                                                                domain.contacts.registrant.email\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCDE \",\n                                                                                domain.contacts.registrant.phone\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCCD \",\n                                                                                domain.contacts.registrant.address\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.registrant.contactId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                domain.contacts.registrant.contactId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 779,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"admin\", {\n                                                                        defaultValue: \"Administrative Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: domain.contacts.admin.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.admin.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-blue-600 font-medium\",\n                                                                            children: domain.contacts.admin.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600 mt-2\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCE7 \",\n                                                                                domain.contacts.admin.email\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 798,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCDE \",\n                                                                                domain.contacts.admin.phone\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCCD \",\n                                                                                domain.contacts.admin.address\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 804,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.admin.contactId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                domain.contacts.admin.contactId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"technical\", {\n                                                                        defaultValue: \"Technical Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: domain.contacts.technical.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.technical.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-blue-600 font-medium\",\n                                                                            children: domain.contacts.technical.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 823,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600 mt-2\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCE7 \",\n                                                                                domain.contacts.technical.email\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCDE \",\n                                                                                domain.contacts.technical.phone\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCCD \",\n                                                                                domain.contacts.technical.address\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 833,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.technical.contactId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                domain.contacts.technical.contactId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 837,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: t(\"billing\", {\n                                                                        defaultValue: \"Billing Contact\"\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: domain.contacts.billing.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 850,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.billing.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-blue-600 font-medium\",\n                                                                            children: domain.contacts.billing.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 854,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600 mt-2\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCE7 \",\n                                                                                domain.contacts.billing.email\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 858,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCDE \",\n                                                                                domain.contacts.billing.phone\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 861,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCCD \",\n                                                                                domain.contacts.billing.address\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 864,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domain.contacts.billing.contactId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                domain.contacts.billing.contactId\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                            lineNumber: 868,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                                        onClick: ()=>router.push(\"/client/domains/\".concat(id, \"/contacts\")),\n                                                        children: dt(\"update_contacts\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                    value: \"privacy\",\n                                    className: \"p-0 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-lg font-medium text-gray-900 mb-6\",\n                                                    children: t(\"privacy\", {\n                                                        defaultValue: \"Privacy Protection\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Globe_Lock_Mail_RefreshCw_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: t(\"privacy_content_coming_soon\", {\n                                                                defaultValue: \"Privacy protection settings will be available soon.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: t(\"privacy_description\", {\n                                                                defaultValue: \"Manage your domain privacy protection and WHOIS information visibility.\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n            lineNumber: 333,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\page.jsx\",\n        lineNumber: 332,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDetailPage, \"0gqgV+UVPjyUaY/PznjCm7ieLM8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DomainDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx\n"));

/***/ })

});