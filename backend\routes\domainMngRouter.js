const express = require("express");
const {
  checkDomainAvailability,
  checkIdnDomainAvailability,
  checkPremiumDomainAvailability,
  suggestDomainNames,
  getDNPricing,
  addDomainToCart,
  getResellerPricing,
  handleDomainSearch,
  syncTldPricing,
  syncDomainPricing,
  getDomainPricing,
  customerSignup,
  registerDomain,
  getDomainDetails,
  getDomainDetailsByName,
  renewDomain,
  modifyNameServers,
  getDomainOrderId,
  getCustomerDefaultNameservers,
  enablePrivacyProtection,
  disablePrivacyProtection,
  purchasePrivacyProtection,
  modifyPrivacyProtection,
  // DNS Record Management
  getDnsRecords,
  addDnsRecord,
  updateDnsRecord,
  deleteDnsRecord,
} = require("../controllers/dn-management/domainMngController");
const { checkUserOrRefreshToken } = require("../midelwares/authorization");

const domainMngRouter = express.Router();

// Customer management

// Domain operations
domainMngRouter.get("/check-domain", checkDomainAvailability);
domainMngRouter.get("/check-idn-domain", checkIdnDomainAvailability);
domainMngRouter.get("/check-premium-domain", checkPremiumDomainAvailability);
domainMngRouter.get("/suggest-names", suggestDomainNames);
// Pricing information
domainMngRouter.get("/get-dn-pricing", getDNPricing);
domainMngRouter.get("/get-reseller-pricing", getResellerPricing);

// Add domain to cart
domainMngRouter.post(
  "/add-domain-to-cart",
  checkUserOrRefreshToken,
  addDomainToCart
);
// Search domains
domainMngRouter.get("/search-domains", handleDomainSearch);
domainMngRouter.post("/sync-tld-pricing", syncTldPricing);
// New routes for syncing and getting domain pricing
domainMngRouter.post("/sync-domain-pricing", syncDomainPricing);
domainMngRouter.get("/get-domain-pricing", getDomainPricing);

// Customer management
domainMngRouter.post("/customer-signup", customerSignup);

// Domain registration and management
domainMngRouter.post("/register-domain", registerDomain);
domainMngRouter.get("/domain-details", getDomainDetails);
domainMngRouter.post("/renew-domain", checkUserOrRefreshToken, renewDomain);

// DNS management
domainMngRouter.get("/domain-order-id", getDomainOrderId);
domainMngRouter.get("/domain-details-by-name", getDomainDetailsByName);
domainMngRouter.get(
  "/customer-default-nameservers",
  getCustomerDefaultNameservers
);
domainMngRouter.post(
  "/modify-nameservers",
  checkUserOrRefreshToken,
  modifyNameServers
);

// Privacy protection
domainMngRouter.post(
  "/enable-privacy",
  checkUserOrRefreshToken,
  enablePrivacyProtection
);
domainMngRouter.post(
  "/disable-privacy",
  checkUserOrRefreshToken,
  disablePrivacyProtection
);
domainMngRouter.post(
  "/purchase-privacy",
  checkUserOrRefreshToken,
  purchasePrivacyProtection
);
domainMngRouter.post(
  "/modify-privacy",
  checkUserOrRefreshToken,
  modifyPrivacyProtection
);

// DNS Record Management
domainMngRouter.get(
  "/domains/:domainId/dns",
  checkUserOrRefreshToken,
  getDnsRecords
);
domainMngRouter.post(
  "/domains/:domainId/dns",
  checkUserOrRefreshToken,
  addDnsRecord
);
domainMngRouter.put(
  "/domains/:domainId/dns/:recordId",
  checkUserOrRefreshToken,
  updateDnsRecord
);
domainMngRouter.delete(
  "/domains/:domainId/dns/:recordId",
  checkUserOrRefreshToken,
  deleteDnsRecord
);

// domainMngRouter.post("/transfer-domain", transferDomain);

// // Theft protection lock
// domainMngRouter.post("/enable-theft-lock", enableTheftProtectionLock);
// domainMngRouter.post("/disable-theft-lock", disableTheftProtectionLock);

// // Domain deletion & restoration
// domainMngRouter.delete("/delete-domain", deleteDomain);
// domainMngRouter.post("/restore-domain", restoreDomain);

// // Contact management
// domainMngRouter.put("/update-contacts", updateDomainContacts);

module.exports = domainMngRouter;
