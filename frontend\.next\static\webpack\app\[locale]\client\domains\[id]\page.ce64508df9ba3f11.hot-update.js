"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/app/services/domainMngService.js":
/*!**********************************************!*\
  !*** ./src/app/services/domainMngService.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst domainMngService = {\n    // Customer management\n    customerSignup: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/customer-signup\", data, {\n            withCredentials: true\n        }),\n    // Domain operations\n    checkDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkIdnDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-idn-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkPremiumDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-premium-domain\", {\n            params,\n            withCredentials: true\n        }),\n    suggestDomainNames: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/suggest-names\", {\n            params,\n            withCredentials: true\n        }),\n    // Pricing information\n    getDNPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-dn-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Get reseller pricing\n    getResellerPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-reseller-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Add domain to cart\n    addDomainToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/add-domain-to-cart\", data, {\n            withCredentials: true\n        }),\n    // Add the new comprehensive search method\n    // Expected params: { params: \"domain-name-string\" }\n    searchDomains: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/search-domains\", {\n            params,\n            withCredentials: true\n        }),\n    // Domain Management - User Domains\n    getUserDomains: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/order/get-domain-orders\", {\n            withCredentials: true\n        }),\n    // Get domain details\n    getDomainDetails: (domainName)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-details\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        }),\n    // Get domain details by name (from registration system)\n    getDomainDetailsByName: function(domainName) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"OrderDetails\";\n        return _lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-details-by-name\", {\n            params: {\n                domainName,\n                options\n            },\n            withCredentials: true\n        });\n    },\n    // Renew domain\n    renewDomain: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/renew-domain\", data, {\n            withCredentials: true\n        }),\n    // Get domain order ID\n    getDomainOrderId: (domainName)=>{\n        console.log(\"\\uD83D\\uDD27 Frontend service - getDomainOrderId called with:\", domainName);\n        console.log(\"\\uD83D\\uDD27 Frontend service - params object:\", {\n            domainName\n        });\n        return _lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-order-id\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        });\n    },\n    // Get customer default nameservers\n    getCustomerDefaultNameservers: (customerId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/customer-default-nameservers\", {\n            params: customerId ? {\n                customerId\n            } : {},\n            withCredentials: true\n        }),\n    // Modify nameservers\n    modifyNameservers: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/modify-nameservers\", data, {\n            withCredentials: true\n        }),\n    // Enable privacy protection\n    enablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/enable-privacy\", data, {\n            withCredentials: true\n        }),\n    // Disable privacy protection\n    disablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/disable-privacy\", data, {\n            withCredentials: true\n        }),\n    // Purchase privacy protection\n    purchasePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/purchase-privacy\", data, {\n            withCredentials: true\n        }),\n    // Modify privacy protection status\n    modifyPrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/modify-privacy\", data, {\n            withCredentials: true\n        }),\n    getDomainById: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId), {\n            withCredentials: true\n        }),\n    // Domain Management - Nameservers\n    updateNameservers: (domainId, nameservers)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/nameservers\"), {\n            nameservers\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Auto Renewal\n    toggleAutoRenewal: (domainId, autoRenew)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/auto-renew\"), {\n            autoRenew\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Privacy Protection\n    togglePrivacyProtection: (domainId, privacyEnabled)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/privacy\"), {\n            privacyEnabled\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - DNS Records\n    activateDnsService: (orderId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/dns/activate\", {\n            orderId\n        }, {\n            withCredentials: true\n        }),\n    getDnsRecords: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            withCredentials: true\n        }),\n    addDnsRecord: (domainId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    updateDnsRecord: (domainId, recordId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    deleteDnsRecord: (domainId, recordId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (domainMngService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvc2VydmljZXMvZG9tYWluTW5nU2VydmljZS5qcyIsIm1hcHBpbmdzIjoiOztBQUEyQztBQUUzQyxNQUFNQyxtQkFBbUI7SUFDdkIsc0JBQXNCO0lBQ3RCQyxnQkFBZ0IsQ0FBQ0MsT0FDZkgsdURBQVVBLENBQUNJLElBQUksQ0FBQyw4QkFBOEJELE1BQU07WUFDbERFLGlCQUFpQjtRQUNuQjtJQUVGLG9CQUFvQjtJQUNwQkMseUJBQXlCLENBQUNDLFNBQ3hCUCx1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDJCQUEyQjtZQUN4Q0Q7WUFDQUYsaUJBQWlCO1FBQ25CO0lBRUZJLDRCQUE0QixDQUFDRixTQUMzQlAsdURBQVVBLENBQUNRLEdBQUcsQ0FBQywrQkFBK0I7WUFDNUNEO1lBQ0FGLGlCQUFpQjtRQUNuQjtJQUVGSyxnQ0FBZ0MsQ0FBQ0gsU0FDL0JQLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsbUNBQW1DO1lBQ2hERDtZQUNBRixpQkFBaUI7UUFDbkI7SUFFRk0sb0JBQW9CLENBQUNKLFNBQ25CUCx1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDRCQUE0QjtZQUN6Q0Q7WUFDQUYsaUJBQWlCO1FBQ25CO0lBRUYsc0JBQXNCO0lBQ3RCTyxjQUFjLENBQUNMLFNBQ2JQLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsNkJBQTZCO1lBQzFDRDtZQUNBRixpQkFBaUI7UUFDbkI7SUFFRix1QkFBdUI7SUFDdkJRLG9CQUFvQixDQUFDTixTQUNuQlAsdURBQVVBLENBQUNRLEdBQUcsQ0FBQyxtQ0FBbUM7WUFDaEREO1lBQ0FGLGlCQUFpQjtRQUNuQjtJQUVGLHFCQUFxQjtJQUNyQlMsaUJBQWlCLENBQUNYLE9BQ2hCSCx1REFBVUEsQ0FBQ0ksSUFBSSxDQUFDLGlDQUFpQ0QsTUFBTTtZQUNyREUsaUJBQWlCO1FBQ25CO0lBRUYsMENBQTBDO0lBQzFDLG9EQUFvRDtJQUNwRFUsZUFBZSxDQUFDUixTQUNkUCx1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDZCQUE2QjtZQUMxQ0Q7WUFDQUYsaUJBQWlCO1FBQ25CO0lBRUYsbUNBQW1DO0lBQ25DVyxnQkFBZ0IsSUFDZGhCLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsNEJBQTRCO1lBQ3pDSCxpQkFBaUI7UUFDbkI7SUFFRixxQkFBcUI7SUFDckJZLGtCQUFrQixDQUFDQyxhQUNqQmxCLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsNkJBQTZCO1lBQzFDRCxRQUFRO2dCQUFFVztZQUFXO1lBQ3JCYixpQkFBaUI7UUFDbkI7SUFFRix3REFBd0Q7SUFDeERjLHdCQUF3QixTQUFDRDtZQUFZRSwyRUFBVTtlQUM3Q3BCLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMscUNBQXFDO1lBQ2xERCxRQUFRO2dCQUFFVztnQkFBWUU7WUFBUTtZQUM5QmYsaUJBQWlCO1FBQ25COztJQUVGLGVBQWU7SUFDZmdCLGFBQWEsQ0FBQ2xCLE9BQ1pILHVEQUFVQSxDQUFDSSxJQUFJLENBQUMsMkJBQTJCRCxNQUFNO1lBQy9DRSxpQkFBaUI7UUFDbkI7SUFFRixzQkFBc0I7SUFDdEJpQixrQkFBa0IsQ0FBQ0o7UUFDakJLLFFBQVFDLEdBQUcsQ0FDVCxpRUFDQU47UUFFRkssUUFBUUMsR0FBRyxDQUFDLGtEQUF3QztZQUFFTjtRQUFXO1FBQ2pFLE9BQU9sQix1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDhCQUE4QjtZQUNsREQsUUFBUTtnQkFBRVc7WUFBVztZQUNyQmIsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxtQ0FBbUM7SUFDbkNvQiwrQkFBK0IsQ0FBQ0MsYUFDOUIxQix1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLDJDQUEyQztZQUN4REQsUUFBUW1CLGFBQWE7Z0JBQUVBO1lBQVcsSUFBSSxDQUFDO1lBQ3ZDckIsaUJBQWlCO1FBQ25CO0lBRUYscUJBQXFCO0lBQ3JCc0IsbUJBQW1CLENBQUN4QixPQUNsQkgsdURBQVVBLENBQUNJLElBQUksQ0FBQyxpQ0FBaUNELE1BQU07WUFDckRFLGlCQUFpQjtRQUNuQjtJQUVGLDRCQUE0QjtJQUM1QnVCLHlCQUF5QixDQUFDekIsT0FDeEJILHVEQUFVQSxDQUFDSSxJQUFJLENBQUMsNkJBQTZCRCxNQUFNO1lBQ2pERSxpQkFBaUI7UUFDbkI7SUFFRiw2QkFBNkI7SUFDN0J3QiwwQkFBMEIsQ0FBQzFCLE9BQ3pCSCx1REFBVUEsQ0FBQ0ksSUFBSSxDQUFDLDhCQUE4QkQsTUFBTTtZQUNsREUsaUJBQWlCO1FBQ25CO0lBRUYsOEJBQThCO0lBQzlCeUIsMkJBQTJCLENBQUMzQixPQUMxQkgsdURBQVVBLENBQUNJLElBQUksQ0FBQywrQkFBK0JELE1BQU07WUFDbkRFLGlCQUFpQjtRQUNuQjtJQUVGLG1DQUFtQztJQUNuQzBCLHlCQUF5QixDQUFDNUIsT0FDeEJILHVEQUFVQSxDQUFDSSxJQUFJLENBQUMsNkJBQTZCRCxNQUFNO1lBQ2pERSxpQkFBaUI7UUFDbkI7SUFFRjJCLGVBQWUsQ0FBQ0MsV0FDZGpDLHVEQUFVQSxDQUFDUSxHQUFHLENBQUMsc0JBQStCLE9BQVR5QixXQUFZO1lBQy9DNUIsaUJBQWlCO1FBQ25CO0lBRUYsa0NBQWtDO0lBQ2xDNkIsbUJBQW1CLENBQUNELFVBQVVFLGNBQzVCbkMsdURBQVVBLENBQUNvQyxHQUFHLENBQ1osc0JBQStCLE9BQVRILFVBQVMsaUJBQy9CO1lBQUVFO1FBQVksR0FDZDtZQUFFOUIsaUJBQWlCO1FBQUs7SUFHNUIsbUNBQW1DO0lBQ25DZ0MsbUJBQW1CLENBQUNKLFVBQVVLLFlBQzVCdEMsdURBQVVBLENBQUNvQyxHQUFHLENBQ1osc0JBQStCLE9BQVRILFVBQVMsZ0JBQy9CO1lBQUVLO1FBQVUsR0FDWjtZQUFFakMsaUJBQWlCO1FBQUs7SUFHNUIseUNBQXlDO0lBQ3pDa0MseUJBQXlCLENBQUNOLFVBQVVPLGlCQUNsQ3hDLHVEQUFVQSxDQUFDb0MsR0FBRyxDQUNaLHNCQUErQixPQUFUSCxVQUFTLGFBQy9CO1lBQUVPO1FBQWUsR0FDakI7WUFBRW5DLGlCQUFpQjtRQUFLO0lBRzVCLGtDQUFrQztJQUNsQ29DLG9CQUFvQixDQUFDQyxVQUNuQjFDLHVEQUFVQSxDQUFDSSxJQUFJLENBQ2IsMkJBQ0E7WUFBRXNDO1FBQVEsR0FDVjtZQUFFckMsaUJBQWlCO1FBQUs7SUFHNUJzQyxlQUFlLENBQUNWLFdBQ2RqQyx1REFBVUEsQ0FBQ1EsR0FBRyxDQUFDLHNCQUErQixPQUFUeUIsVUFBUyxTQUFPO1lBQ25ENUIsaUJBQWlCO1FBQ25CO0lBRUZ1QyxjQUFjLENBQUNYLFVBQVVZLFNBQ3ZCN0MsdURBQVVBLENBQUNJLElBQUksQ0FDYixzQkFBK0IsT0FBVDZCLFVBQVMsU0FDL0I7WUFBRVk7UUFBTyxHQUNUO1lBQUV4QyxpQkFBaUI7UUFBSztJQUc1QnlDLGlCQUFpQixDQUFDYixVQUFVYyxVQUFVRixTQUNwQzdDLHVEQUFVQSxDQUFDb0MsR0FBRyxDQUNaLHNCQUFzQ1csT0FBaEJkLFVBQVMsU0FBZ0IsT0FBVGMsV0FDdEM7WUFBRUY7UUFBTyxHQUNUO1lBQUV4QyxpQkFBaUI7UUFBSztJQUc1QjJDLGlCQUFpQixDQUFDZixVQUFVYyxXQUMxQi9DLHVEQUFVQSxDQUFDaUQsTUFBTSxDQUFDLHNCQUFzQ0YsT0FBaEJkLFVBQVMsU0FBZ0IsT0FBVGMsV0FBWTtZQUNsRTFDLGlCQUFpQjtRQUNuQjtBQUNKO0FBRUEsK0RBQWVKLGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3NlcnZpY2VzL2RvbWFpbk1uZ1NlcnZpY2UuanM/NDU0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXBpU2VydmljZSBmcm9tIFwiLi4vbGliL2FwaVNlcnZpY2VcIjtcclxuXHJcbmNvbnN0IGRvbWFpbk1uZ1NlcnZpY2UgPSB7XHJcbiAgLy8gQ3VzdG9tZXIgbWFuYWdlbWVudFxyXG4gIGN1c3RvbWVyU2lnbnVwOiAoZGF0YSkgPT5cclxuICAgIGFwaVNlcnZpY2UucG9zdChcIi9kb21haW5NbmcvY3VzdG9tZXItc2lnbnVwXCIsIGRhdGEsIHtcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIC8vIERvbWFpbiBvcGVyYXRpb25zXHJcbiAgY2hlY2tEb21haW5BdmFpbGFiaWxpdHk6IChwYXJhbXMpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvY2hlY2stZG9tYWluXCIsIHtcclxuICAgICAgcGFyYW1zLFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgY2hlY2tJZG5Eb21haW5BdmFpbGFiaWxpdHk6IChwYXJhbXMpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvY2hlY2staWRuLWRvbWFpblwiLCB7XHJcbiAgICAgIHBhcmFtcyxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIGNoZWNrUHJlbWl1bURvbWFpbkF2YWlsYWJpbGl0eTogKHBhcmFtcykgPT5cclxuICAgIGFwaVNlcnZpY2UuZ2V0KFwiL2RvbWFpbk1uZy9jaGVjay1wcmVtaXVtLWRvbWFpblwiLCB7XHJcbiAgICAgIHBhcmFtcyxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIHN1Z2dlc3REb21haW5OYW1lczogKHBhcmFtcykgPT5cclxuICAgIGFwaVNlcnZpY2UuZ2V0KFwiL2RvbWFpbk1uZy9zdWdnZXN0LW5hbWVzXCIsIHtcclxuICAgICAgcGFyYW1zLFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gUHJpY2luZyBpbmZvcm1hdGlvblxyXG4gIGdldEROUHJpY2luZzogKHBhcmFtcykgPT5cclxuICAgIGFwaVNlcnZpY2UuZ2V0KFwiL2RvbWFpbk1uZy9nZXQtZG4tcHJpY2luZ1wiLCB7XHJcbiAgICAgIHBhcmFtcyxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIC8vIEdldCByZXNlbGxlciBwcmljaW5nXHJcbiAgZ2V0UmVzZWxsZXJQcmljaW5nOiAocGFyYW1zKSA9PlxyXG4gICAgYXBpU2VydmljZS5nZXQoXCIvZG9tYWluTW5nL2dldC1yZXNlbGxlci1wcmljaW5nXCIsIHtcclxuICAgICAgcGFyYW1zLFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gQWRkIGRvbWFpbiB0byBjYXJ0XHJcbiAgYWRkRG9tYWluVG9DYXJ0OiAoZGF0YSkgPT5cclxuICAgIGFwaVNlcnZpY2UucG9zdChcIi9kb21haW5NbmcvYWRkLWRvbWFpbi10by1jYXJ0XCIsIGRhdGEsIHtcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIC8vIEFkZCB0aGUgbmV3IGNvbXByZWhlbnNpdmUgc2VhcmNoIG1ldGhvZFxyXG4gIC8vIEV4cGVjdGVkIHBhcmFtczogeyBwYXJhbXM6IFwiZG9tYWluLW5hbWUtc3RyaW5nXCIgfVxyXG4gIHNlYXJjaERvbWFpbnM6IChwYXJhbXMpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5Nbmcvc2VhcmNoLWRvbWFpbnNcIiwge1xyXG4gICAgICBwYXJhbXMsXHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBEb21haW4gTWFuYWdlbWVudCAtIFVzZXIgRG9tYWluc1xyXG4gIGdldFVzZXJEb21haW5zOiAoKSA9PlxyXG4gICAgYXBpU2VydmljZS5nZXQoXCIvb3JkZXIvZ2V0LWRvbWFpbi1vcmRlcnNcIiwge1xyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gR2V0IGRvbWFpbiBkZXRhaWxzXHJcbiAgZ2V0RG9tYWluRGV0YWlsczogKGRvbWFpbk5hbWUpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvZG9tYWluLWRldGFpbHNcIiwge1xyXG4gICAgICBwYXJhbXM6IHsgZG9tYWluTmFtZSB9LFxyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgLy8gR2V0IGRvbWFpbiBkZXRhaWxzIGJ5IG5hbWUgKGZyb20gcmVnaXN0cmF0aW9uIHN5c3RlbSlcclxuICBnZXREb21haW5EZXRhaWxzQnlOYW1lOiAoZG9tYWluTmFtZSwgb3B0aW9ucyA9IFwiT3JkZXJEZXRhaWxzXCIpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvZG9tYWluLWRldGFpbHMtYnktbmFtZVwiLCB7XHJcbiAgICAgIHBhcmFtczogeyBkb21haW5OYW1lLCBvcHRpb25zIH0sXHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBSZW5ldyBkb21haW5cclxuICByZW5ld0RvbWFpbjogKGRhdGEpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnBvc3QoXCIvZG9tYWluTW5nL3JlbmV3LWRvbWFpblwiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBHZXQgZG9tYWluIG9yZGVyIElEXHJcbiAgZ2V0RG9tYWluT3JkZXJJZDogKGRvbWFpbk5hbWUpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICBcIvCflKcgRnJvbnRlbmQgc2VydmljZSAtIGdldERvbWFpbk9yZGVySWQgY2FsbGVkIHdpdGg6XCIsXHJcbiAgICAgIGRvbWFpbk5hbWVcclxuICAgICk7XHJcbiAgICBjb25zb2xlLmxvZyhcIvCflKcgRnJvbnRlbmQgc2VydmljZSAtIHBhcmFtcyBvYmplY3Q6XCIsIHsgZG9tYWluTmFtZSB9KTtcclxuICAgIHJldHVybiBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvZG9tYWluLW9yZGVyLWlkXCIsIHtcclxuICAgICAgcGFyYW1zOiB7IGRvbWFpbk5hbWUgfSxcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IGN1c3RvbWVyIGRlZmF1bHQgbmFtZXNlcnZlcnNcclxuICBnZXRDdXN0b21lckRlZmF1bHROYW1lc2VydmVyczogKGN1c3RvbWVySWQpID0+XHJcbiAgICBhcGlTZXJ2aWNlLmdldChcIi9kb21haW5NbmcvY3VzdG9tZXItZGVmYXVsdC1uYW1lc2VydmVyc1wiLCB7XHJcbiAgICAgIHBhcmFtczogY3VzdG9tZXJJZCA/IHsgY3VzdG9tZXJJZCB9IDoge30sXHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBNb2RpZnkgbmFtZXNlcnZlcnNcclxuICBtb2RpZnlOYW1lc2VydmVyczogKGRhdGEpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnBvc3QoXCIvZG9tYWluTW5nL21vZGlmeS1uYW1lc2VydmVyc1wiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBFbmFibGUgcHJpdmFjeSBwcm90ZWN0aW9uXHJcbiAgZW5hYmxlUHJpdmFjeVByb3RlY3Rpb246IChkYXRhKSA9PlxyXG4gICAgYXBpU2VydmljZS5wb3N0KFwiL2RvbWFpbk1uZy9lbmFibGUtcHJpdmFjeVwiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBEaXNhYmxlIHByaXZhY3kgcHJvdGVjdGlvblxyXG4gIGRpc2FibGVQcml2YWN5UHJvdGVjdGlvbjogKGRhdGEpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnBvc3QoXCIvZG9tYWluTW5nL2Rpc2FibGUtcHJpdmFjeVwiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBQdXJjaGFzZSBwcml2YWN5IHByb3RlY3Rpb25cclxuICBwdXJjaGFzZVByaXZhY3lQcm90ZWN0aW9uOiAoZGF0YSkgPT5cclxuICAgIGFwaVNlcnZpY2UucG9zdChcIi9kb21haW5NbmcvcHVyY2hhc2UtcHJpdmFjeVwiLCBkYXRhLCB7XHJcbiAgICAgIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxuICAgIH0pLFxyXG5cclxuICAvLyBNb2RpZnkgcHJpdmFjeSBwcm90ZWN0aW9uIHN0YXR1c1xyXG4gIG1vZGlmeVByaXZhY3lQcm90ZWN0aW9uOiAoZGF0YSkgPT5cclxuICAgIGFwaVNlcnZpY2UucG9zdChcIi9kb21haW5NbmcvbW9kaWZ5LXByaXZhY3lcIiwgZGF0YSwge1xyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgZ2V0RG9tYWluQnlJZDogKGRvbWFpbklkKSA9PlxyXG4gICAgYXBpU2VydmljZS5nZXQoYC9kb21haW5NbmcvZG9tYWlucy8ke2RvbWFpbklkfWAsIHtcclxuICAgICAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gIC8vIERvbWFpbiBNYW5hZ2VtZW50IC0gTmFtZXNlcnZlcnNcclxuICB1cGRhdGVOYW1lc2VydmVyczogKGRvbWFpbklkLCBuYW1lc2VydmVycykgPT5cclxuICAgIGFwaVNlcnZpY2UucHV0KFxyXG4gICAgICBgL2RvbWFpbk1uZy9kb21haW5zLyR7ZG9tYWluSWR9L25hbWVzZXJ2ZXJzYCxcclxuICAgICAgeyBuYW1lc2VydmVycyB9LFxyXG4gICAgICB7IHdpdGhDcmVkZW50aWFsczogdHJ1ZSB9XHJcbiAgICApLFxyXG5cclxuICAvLyBEb21haW4gTWFuYWdlbWVudCAtIEF1dG8gUmVuZXdhbFxyXG4gIHRvZ2dsZUF1dG9SZW5ld2FsOiAoZG9tYWluSWQsIGF1dG9SZW5ldykgPT5cclxuICAgIGFwaVNlcnZpY2UucHV0KFxyXG4gICAgICBgL2RvbWFpbk1uZy9kb21haW5zLyR7ZG9tYWluSWR9L2F1dG8tcmVuZXdgLFxyXG4gICAgICB7IGF1dG9SZW5ldyB9LFxyXG4gICAgICB7IHdpdGhDcmVkZW50aWFsczogdHJ1ZSB9XHJcbiAgICApLFxyXG5cclxuICAvLyBEb21haW4gTWFuYWdlbWVudCAtIFByaXZhY3kgUHJvdGVjdGlvblxyXG4gIHRvZ2dsZVByaXZhY3lQcm90ZWN0aW9uOiAoZG9tYWluSWQsIHByaXZhY3lFbmFibGVkKSA9PlxyXG4gICAgYXBpU2VydmljZS5wdXQoXHJcbiAgICAgIGAvZG9tYWluTW5nL2RvbWFpbnMvJHtkb21haW5JZH0vcHJpdmFjeWAsXHJcbiAgICAgIHsgcHJpdmFjeUVuYWJsZWQgfSxcclxuICAgICAgeyB3aXRoQ3JlZGVudGlhbHM6IHRydWUgfVxyXG4gICAgKSxcclxuXHJcbiAgLy8gRG9tYWluIE1hbmFnZW1lbnQgLSBETlMgUmVjb3Jkc1xyXG4gIGFjdGl2YXRlRG5zU2VydmljZTogKG9yZGVySWQpID0+XHJcbiAgICBhcGlTZXJ2aWNlLnBvc3QoXHJcbiAgICAgIFwiL2RvbWFpbk1uZy9kbnMvYWN0aXZhdGVcIixcclxuICAgICAgeyBvcmRlcklkIH0sXHJcbiAgICAgIHsgd2l0aENyZWRlbnRpYWxzOiB0cnVlIH1cclxuICAgICksXHJcblxyXG4gIGdldERuc1JlY29yZHM6IChkb21haW5JZCkgPT5cclxuICAgIGFwaVNlcnZpY2UuZ2V0KGAvZG9tYWluTW5nL2RvbWFpbnMvJHtkb21haW5JZH0vZG5zYCwge1xyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxuXHJcbiAgYWRkRG5zUmVjb3JkOiAoZG9tYWluSWQsIHJlY29yZCkgPT5cclxuICAgIGFwaVNlcnZpY2UucG9zdChcclxuICAgICAgYC9kb21haW5NbmcvZG9tYWlucy8ke2RvbWFpbklkfS9kbnNgLFxyXG4gICAgICB7IHJlY29yZCB9LFxyXG4gICAgICB7IHdpdGhDcmVkZW50aWFsczogdHJ1ZSB9XHJcbiAgICApLFxyXG5cclxuICB1cGRhdGVEbnNSZWNvcmQ6IChkb21haW5JZCwgcmVjb3JkSWQsIHJlY29yZCkgPT5cclxuICAgIGFwaVNlcnZpY2UucHV0KFxyXG4gICAgICBgL2RvbWFpbk1uZy9kb21haW5zLyR7ZG9tYWluSWR9L2Rucy8ke3JlY29yZElkfWAsXHJcbiAgICAgIHsgcmVjb3JkIH0sXHJcbiAgICAgIHsgd2l0aENyZWRlbnRpYWxzOiB0cnVlIH1cclxuICAgICksXHJcblxyXG4gIGRlbGV0ZURuc1JlY29yZDogKGRvbWFpbklkLCByZWNvcmRJZCkgPT5cclxuICAgIGFwaVNlcnZpY2UuZGVsZXRlKGAvZG9tYWluTW5nL2RvbWFpbnMvJHtkb21haW5JZH0vZG5zLyR7cmVjb3JkSWR9YCwge1xyXG4gICAgICB3aXRoQ3JlZGVudGlhbHM6IHRydWUsXHJcbiAgICB9KSxcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGRvbWFpbk1uZ1NlcnZpY2U7XHJcbiJdLCJuYW1lcyI6WyJhcGlTZXJ2aWNlIiwiZG9tYWluTW5nU2VydmljZSIsImN1c3RvbWVyU2lnbnVwIiwiZGF0YSIsInBvc3QiLCJ3aXRoQ3JlZGVudGlhbHMiLCJjaGVja0RvbWFpbkF2YWlsYWJpbGl0eSIsInBhcmFtcyIsImdldCIsImNoZWNrSWRuRG9tYWluQXZhaWxhYmlsaXR5IiwiY2hlY2tQcmVtaXVtRG9tYWluQXZhaWxhYmlsaXR5Iiwic3VnZ2VzdERvbWFpbk5hbWVzIiwiZ2V0RE5QcmljaW5nIiwiZ2V0UmVzZWxsZXJQcmljaW5nIiwiYWRkRG9tYWluVG9DYXJ0Iiwic2VhcmNoRG9tYWlucyIsImdldFVzZXJEb21haW5zIiwiZ2V0RG9tYWluRGV0YWlscyIsImRvbWFpbk5hbWUiLCJnZXREb21haW5EZXRhaWxzQnlOYW1lIiwib3B0aW9ucyIsInJlbmV3RG9tYWluIiwiZ2V0RG9tYWluT3JkZXJJZCIsImNvbnNvbGUiLCJsb2ciLCJnZXRDdXN0b21lckRlZmF1bHROYW1lc2VydmVycyIsImN1c3RvbWVySWQiLCJtb2RpZnlOYW1lc2VydmVycyIsImVuYWJsZVByaXZhY3lQcm90ZWN0aW9uIiwiZGlzYWJsZVByaXZhY3lQcm90ZWN0aW9uIiwicHVyY2hhc2VQcml2YWN5UHJvdGVjdGlvbiIsIm1vZGlmeVByaXZhY3lQcm90ZWN0aW9uIiwiZ2V0RG9tYWluQnlJZCIsImRvbWFpbklkIiwidXBkYXRlTmFtZXNlcnZlcnMiLCJuYW1lc2VydmVycyIsInB1dCIsInRvZ2dsZUF1dG9SZW5ld2FsIiwiYXV0b1JlbmV3IiwidG9nZ2xlUHJpdmFjeVByb3RlY3Rpb24iLCJwcml2YWN5RW5hYmxlZCIsImFjdGl2YXRlRG5zU2VydmljZSIsIm9yZGVySWQiLCJnZXREbnNSZWNvcmRzIiwiYWRkRG5zUmVjb3JkIiwicmVjb3JkIiwidXBkYXRlRG5zUmVjb3JkIiwicmVjb3JkSWQiLCJkZWxldGVEbnNSZWNvcmQiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/domainMngService.js\n"));

/***/ })

});